This is the Gemini CLI. We are setting up the context for our chat.
Today's date is 2025年8月29日星期五 (formatted according to the user's locale).
My operating system is: win32
I'm currently working in the directory: C:\Users\<USER>\Downloads\createjob refactory\plain
Here is the folder structure of the current working directories:

Showing up to 200 items (files + folders).

C:\Users\<USER>\Downloads\createjob refactory\plain\
├───.claude\
│   └───settings.local.json
├───channel-detection-editor\
│   ├───.claude\
│   │   └───settings.local.json
│   ├───address-translator.js
│   ├───airport-data.js
│   ├───app.js
│   ├───build.js
│   ├───cache-integration-adapter.js
│   ├───cache-manager.js
│   ├───cache-monitor-panel.js
│   ├───channel-config.js
│   ├───channel-detector.js
│   ├───config.js
│   ├───crypto-utils.js
│   ├───data.js
│   ├───error-handler.js
│   ├───field-mapper.js
│   ├───gemini-config.js
│   ├───index.html
│   ├───local-storage-manager.js
│   ├───maintenance-workflow-manager.js
│   ├───module-container.js
│   ├───prompt-composer.js
│   ├───prompt-editor.js
│   ├───prompt-fragments.js
│   ├───prompt-segmenter.js
│   ├───rule-editor.js
│   ├───user-config.js
│   ├───user-workflow-enhancer.js
│   ├───workflow-dev-optimizer.js
│   └───workflow-integration-loader.js
├───docs\
│   ├───api\
│   │   ├───api-return-id-list.md
│   │   ├───gomyhire-api-field-requirements.md
│   │   └───static-data-record.md
│   ├───architecture\
│   │   ├───architecture-review.md
│   │   └───cache-system-guide.md
│   ├───developer\
│   │   ├───build-instructions.md
│   │   ├───changelog.md
│   │   ├───code-review.md
│   │   ├───local-setup.md
│   │   ├───migration-log.md
│   │   ├───project-overview.md
│   │   └───reports\
│   │       ├───comprehensive-test-report.md
│   │       ├───implementation-complete.md
│   │       ├───prompt-optimization-report.md
│   │       └───refactoring-complete-report.md
│   └───user-guide\
│       └───README.md
├───hotels_by_region.js
└───tests\
    ├───integration\
    │   ├───automated-test-runner.html
    │   └───comprehensive-test-suite.html
    ├───performance\
    │   ├───cache-performance-test.html
    │   └───workflow-efficiency-monitor.html
    └───unit\

# Project Overview

This project is a "Channel Detection Editor," a standalone, browser-based tool for parsing and extracting structured data from unstructured travel booking information (e.g., emails, text messages). Its core function is to identify the booking channel (like Klook, Ctrip, etc.) and map the text into a clean JSON object with fields like customer name, contact, pickup/dropoff locations, and flight info.

The application is built with vanilla JavaScript and has recently undergone a significant architectural refactoring. It now uses a custom **Dependency Injection (DI)** system (`module-container.js`) to manage its components, moving away from a reliance on global variables.

A key feature is its sophisticated, multi-layered **caching system** (`cache-manager.js`) that supports memory and localStorage backends, TTL, and LRU eviction to optimize performance, especially for expensive Gemini API calls. The core data extraction logic relies on the Gemini API, orchestrated by the `field-mapper.js` module.

# Building and Running

This is a static, browser-based project with no build process.

*   **To run the application:** Open the `channel-detection-editor/index.html` file in a web browser.
*   **To run tests:** Open the various `.html` files in the `tests/` directory in a web browser.
    *   `tests/integration/`: Contains suites for comprehensive and automated testing.
    *   `tests/performance/`: Contains tools for benchmarking cache performance and monitoring workflow efficiency.

# Development Conventions

*   **Architecture:** The project follows a modular architecture orchestrated by a dependency injection container (`module-container.js`). Modules are registered as factories and dependencies are injected via the container. This is the preferred pattern for new development.
*   **Service Access:** Services should be accessed via the module container (`container.get('serviceName')`). Global variables are being phased out but are supported for backward compatibility.
*   **Core Logic:**
    *   **Channel Detection (`channel-detector.js`):** Uses a weighted, rule-based system to identify booking channels.
    *   **Field Mapping (`field-mapper.js`):** Relies on the Gemini API for primary data extraction. It uses a `PromptFragmentManager` to construct context-aware prompts.
    *   **Caching (`cache-manager.js`):** A crucial utility for performance. It provides different caching strategies for different types of data. New features that involve fetching data should consider leveraging this service.
*   **Testing:** Tests are self-contained HTML files that can be run directly in the browser. They load the necessary application scripts and perform assertions within the page.
*   **Documentation:** All project documentation has been consolidated into the `/docs` directory, categorized by type (API, Architecture, Developer, User Guide).