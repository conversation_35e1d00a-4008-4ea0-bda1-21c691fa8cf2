/**
 * 构建脚本模块 - 简单的构建脚本 - 合并和压缩JS文件
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：Node.js fs/path模块, terser压缩库
 * 被依赖：无（独立构建工具）
 * 构建工具：文件合并、代码压缩、依赖排序、版本管理
 * 
 * === 核心功能 ===
 * - 多JavaScript文件的合并和排序
 * - 代码压缩和优化（使用terser）
 * - 构建版本管理和时间戳
 * - 文件依赖顺序管理
 * - 构建错误处理和日志
 * 
 * === 集成点 ===
 * - 独立构建工具，不依赖其他应用模块
 * - 需要Node.js环境和terser依赖
 * - 生成合并后的生产版本文件
 * 
 * === 使用场景 ===
 * - 生产环境代码打包和优化
 * - 多文件依赖合并和顺序管理
 * - 代码压缩和性能优化
 * - 构建版本管理和部署
 * 
 * === 注意事项 ===
 * 简单的构建脚本 - 合并和压缩JS文件
 * 文件加载顺序非常重要，必须保持正确依赖关系
 * 需要安装 terser: npm install terser
 * 生成的文件包含构建时间戳和版本信息
 */

// 简单的构建脚本 - 合并和压缩JS文件

const fs = require('fs');
const path = require('path');
const { minify } = require('terser');

// 文件加载顺序（重要！）
const jsFiles = [
  'config.js',
  'gemini-config.js', 
  'data.js',
  'channel-detector.js',
  'field-mapper.js',
  'crypto-utils.js',
  'local-storage-manager.js',
  'rule-editor.js',
  'prompt-editor.js',
  'prompt-segmenter.js',
  'prompt-composer.js',
  'app.js'
];

async function build() {
  console.log('🚀 开始构建渠道检测编辑器...');
  
  let combinedCode = `// 渠道检测编辑器 - 合并构建版本
// 构建时间: ${new Date().toISOString()}
// 文件数: ${jsFiles.length}

`;
  
  // 读取并合并所有文件
  for (const file of jsFiles) {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      combinedCode += `\n// ===== ${file} =====\n`;
      combinedCode += content;
      combinedCode += '\n';
      console.log(`✅ 已添加: ${file}`);
    } else {
      console.warn(`⚠️  文件不存在: ${file}`);
    }
  }
  
  // 保存未压缩的合并文件
  fs.writeFileSync('app-combined.js', combinedCode);
  console.log(`📦 合并文件大小: ${(combinedCode.length / 1024).toFixed(2)} KB`);
  
  try {
    // 压缩代码
    const result = await minify(combinedCode, {
      compress: {
        drop_console: false, // 保留console日志
        drop_debugger: true,
        ecma: 2015,
      },
      mangle: {
        toplevel: true,
      },
      format: {
        comments: false,
      }
    });
    
    if (result.code) {
      fs.writeFileSync('app.min.js', result.code);
      const originalSize = combinedCode.length;
      const compressedSize = result.code.length;
      const ratio = (compressedSize / originalSize * 100).toFixed(1);
      
      console.log(`🎯 压缩完成: ${(compressedSize / 1024).toFixed(2)} KB (${ratio}% 原始大小)`);
      console.log(`📊 压缩比例: ${(originalSize / 1024).toFixed(2)} KB → ${(compressedSize / 1024).toFixed(2)} KB`);
      
      // 生成优化的index.html
      generateOptimizedHTML();
      
    } else {
      console.error('❌ 压缩失败');
    }
    
  } catch (error) {
    console.error('❌ 压缩错误:', error.message);
  }
}

function generateOptimizedHTML() {
  const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道检测编辑器 - 优化版</title>
    <style>
        /* 原有CSS样式保持不变 */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; text-align: center; }
        /* ... 其他样式保持不变 ... */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 渠道检测编辑器</h1>
            <p>优化版本 - 加载更快，性能更好</p>
        </div>

        <div class="main-content">
            <div class="section input-section">
                <h2>📝 输入内容</h2>
                <textarea id="inputContent" placeholder="请输入订单内容..."></textarea>
                
                <div class="button-group">
                    <button class="btn-primary" onclick="processInput()">处理输入</button>
                    <button class="btn-secondary" onclick="clearInput()">清空</button>
                    <button class="btn-secondary" onclick="openRuleEditor()" style="background: #fd7e14;">🛠️ 编辑规则</button>
                    <button class="btn-secondary" onclick="openPromptEditor()" style="background: #6f42c1;">📝 编辑提示词</button>
                    <button class="btn-secondary" onclick="showGeminiConfig()" style="background: #10b981;">🔑 API设置</button>
                </div>
            </div>

            <div class="section">
                <h2>📊 处理结果</h2>
                <div class="result-section" id="resultContainer">
                    <div class="result-item">
                        <strong>等待处理...</strong>
                        <p>请输入内容并点击"处理输入"按钮</p>
                    </div>
                </div>

                <div class="channel-detection">
                    <h3>🔍 渠道检测结果</h3>
                    <div class="detection-result" id="channelResult">
                        <h4>未检测</h4>
                        <p>等待渠道检测...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" style="grid-column: 1 / -1; margin: 30px;">
            <h2>🗂️ 字段映射展示</h2>
            <div class="field-mapping" id="fieldMapping">
                <div class="field-card">
                    <h4>customer_name</h4>
                    <p>客户姓名</p>
                    <div class="field-value">-</div>
                </div>
                <div class="field-card">
                    <h4>customer_contact</h4>
                    <p>客户联系电话</p>
                    <div class="field-value">-</div>
                </div>
                <div class="field-card">
                    <h4>pickup</h4>
                    <p>接客地点</p>
                    <div class="field-value">-</div>
                </div>
                <div class="field-card">
                    <h4>destination</h4>
                    <p>目的地</p>
                    <div class="field-value">-</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 合并压缩后的单个JS文件 -->
    <script src="app.min.js"></script>
    
    <!-- 加载状态提示 -->
    <script>
        console.log('🚀 渠道检测编辑器优化版加载中...');
        
        // 显示加载状态
  const loadingStyle = document.createElement('style');
  loadingStyle.textContent = '\n            .loading-overlay {\n                position: fixed; top: 0; left: 0; width: 100%; height: 100%; \n                background: rgba(255,255,255,0.9); display: flex; justify-content: center; \n                align-items: center; z-index: 9999; font-family: monospace;\n            }\n            .loading-spinner {\n                border: 4px solid #f3f3f3; border-top: 4px solid #4facfe; \n                border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite;\n            }\n            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }\n        ';
        document.head.appendChild(loadingStyle);
        
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = '<div><div class="loading-spinner"></div><p style="margin-top: 20px;">加载优化版本...</p></div>';
        document.body.appendChild(overlay);
        
        // 页面加载完成后移除加载提示
        window.addEventListener('load', () => {
            setTimeout(() => {
                overlay.style.opacity = '0';
                setTimeout(() => overlay.remove(), 500);
            }, 1000);
        });
    </script>
</body>
</html>`;
  
  fs.writeFileSync('index-optimized.html', htmlContent);
  console.log('✅ 优化版HTML已生成: index-optimized.html');
}

// 执行构建
build().catch(console.error);