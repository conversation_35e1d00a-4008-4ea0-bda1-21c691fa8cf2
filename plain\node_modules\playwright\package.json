{"name": "playwright", "version": "1.55.0", "description": "A high-level API to automate web browsers", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "main": "index.js", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./package.json": "./package.json", "./lib/common/configLoader": "./lib/common/configLoader.js", "./lib/fsWatcher": "./lib/fsWatcher.js", "./lib/mcp": "./lib/mcp/exports.js", "./lib/program": "./lib/program.js", "./lib/reporters/base": "./lib/reporters/base.js", "./lib/reporters/list": "./lib/reporters/list.js", "./lib/transform/babelBundle": "./lib/transform/babelBundle.js", "./lib/transform/compilationCache": "./lib/transform/compilationCache.js", "./lib/transform/esmLoader": "./lib/transform/esmLoader.js", "./lib/transform/transform": "./lib/transform/transform.js", "./lib/internalsForTest": "./lib/internalsForTest.js", "./lib/plugins": "./lib/plugins/index.js", "./lib/runner/testRunner": "./lib/runner/testRunner.js", "./jsx-runtime": {"import": "./jsx-runtime.mjs", "require": "./jsx-runtime.js", "default": "./jsx-runtime.js"}, "./lib/util": "./lib/util.js", "./lib/utilsBundle": "./lib/utilsBundle.js", "./types/test": {"types": "./types/test.d.ts"}, "./types/testReporter": {"types": "./types/testReporter.d.ts"}, "./test": {"types": "./test.d.ts", "import": "./test.mjs", "require": "./test.js", "default": "./test.js"}}, "bin": {"playwright": "cli.js"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "dependencies": {"playwright-core": "1.55.0"}, "optionalDependencies": {"fsevents": "2.3.2"}}