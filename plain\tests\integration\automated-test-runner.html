<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化测试执行器 - 系统验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-progress {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
            width: 0%;
        }
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .result-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #4facfe;
        }
        .result-card.success { border-left-color: #28a745; }
        .result-card.warning { border-left-color: #ffc107; }
        .result-card.error { border-left-color: #dc3545; }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .metric-value {
            font-weight: bold;
            color: #495057;
        }
        .log-output {
            background: #1a1a1a;
            color: #e5e5e5;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #3a9cfd;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-pending { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 自动化测试执行器</h1>
            <p>渠道检测编辑器重构验证 - 实时性能分析与质量评估</p>
        </div>

        <div class="content">
            <div class="test-progress">
                <h3>测试执行进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div id="progress-text">准备开始测试...</div>
                <div style="margin-top: 10px;">
                    <button onclick="startAutomatedTest()">开始自动化测试</button>
                    <button onclick="clearResults()">清空结果</button>
                    <button onclick="exportReport()" style="background: #fd7e14;">导出报告</button>
                </div>
            </div>

            <div class="results-grid" id="results-grid">
                <!-- 结果卡片将动态生成 -->
            </div>

            <div class="log-output" id="log-output">
                等待测试开始...
            </div>
        </div>
    </div>

    <!-- 加载系统脚本 -->
    <script src="../../channel-detection-editor/module-container.js"></script>
    <script src="../../channel-detection-editor/cache-manager.js"></script>
    <script src="../../channel-detection-editor/cache-integration-adapter.js"></script>
    <script src="../../channel-detection-editor/cache-monitor-panel.js"></script>
    <script src="../../channel-detection-editor/error-handler.js"></script>
    <script src="../../channel-detection-editor/crypto-utils.js"></script>
    <script src="../../channel-detection-editor/local-storage-manager.js"></script>
    <script src="../../channel-detection-editor/data.js"></script>
    <script src="../../hotels_by_region.js"></script>
    <script src="../../channel-detection-editor/airport-data.js"></script>
    <script src="../../channel-detection-editor/config.js"></script>
    <script src="../../channel-detection-editor/gemini-config.js"></script>
    <script src="../../channel-detection-editor/prompt-segmenter.js"></script>
    <script src="../../channel-detection-editor/prompt-composer.js"></script>
    <script src="../../channel-detection-editor/address-translator.js"></script>
    <script src="../../channel-detection-editor/channel-detector.js"></script>
    <script src="../../channel-detection-editor/field-mapper.js"></script>
    <script src="../../channel-detection-editor/rule-editor.js"></script>
    <script src="../../channel-detection-editor/prompt-editor.js"></script>
    <script src="../../channel-detection-editor/app.js"></script>

    <script>
        // 测试状态管理
        let testState = {
            isRunning: false,
            currentTest: 0,
            totalTests: 0,
            results: [],
            startTime: null,
            endTime: null
        };

        // 日志函数
        function log(message, level = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            
            const colors = {
                info: '#e5e5e5',
                success: '#4ade80',
                warning: '#fbbf24',
                error: '#ef4444'
            };
            
            logOutput.innerHTML += `<div style="color: ${colors[level]};">[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 更新进度条
        function updateProgress(percentage, text) {
            document.getElementById('progress-fill').style.width = `${percentage}%`;
            document.getElementById('progress-text').textContent = text;
        }

        // 创建结果卡片
        function createResultCard(title, status, metrics) {
            const card = document.createElement('div');
            card.className = `result-card ${status}`;
            
            let html = `<h4><span class="status-indicator status-${status}"></span>${title}</h4>`;
            
            for (const [key, value] of Object.entries(metrics)) {
                html += `<div class="metric"><span>${key}</span><span class="metric-value">${value}</span></div>`;
            }
            
            card.innerHTML = html;
            return card;
        }

        // 主测试函数
        async function startAutomatedTest() {
            if (testState.isRunning) {
                log('测试已在运行中...', 'warning');
                return;
            }

            testState.isRunning = true;
            testState.startTime = Date.now();
            testState.results = [];
            testState.currentTest = 0;
            
            // 清空结果区域
            document.getElementById('results-grid').innerHTML = '';
            
            log('🚀 开始自动化测试执行...', 'success');
            
            const tests = [
                { name: '系统初始化验证', func: testSystemInitialization },
                { name: '模块容器架构', func: testModuleContainerArchitecture },
                { name: '依赖注入机制', func: testDependencyInjectionMechanism },
                { name: '缓存系统性能', func: testCacheSystemPerformance },
                { name: '核心功能验证', func: testCoreFunctionality },
                { name: '性能基准测试', func: testPerformanceBenchmark },
                { name: '内存使用分析', func: testMemoryUsageAnalysis },
                { name: '错误处理机制', func: testErrorHandlingMechanism },
                { name: '兼容性验证', func: testCompatibilityValidation },
                { name: '回归测试检查', func: testRegressionCheck }
            ];

            testState.totalTests = tests.length;
            
            try {
                for (let i = 0; i < tests.length; i++) {
                    const test = tests[i];
                    testState.currentTest = i + 1;
                    
                    updateProgress((i / tests.length) * 100, `执行: ${test.name}...`);
                    log(`📋 执行测试 ${i + 1}/${tests.length}: ${test.name}`, 'info');
                    
                    try {
                        const result = await test.func();
                        testState.results.push({ name: test.name, ...result });
                        
                        // 创建结果卡片
                        const card = createResultCard(test.name, result.status, result.metrics);
                        document.getElementById('results-grid').appendChild(card);
                        
                        log(`✅ ${test.name} - ${result.status}`, result.status === 'success' ? 'success' : result.status === 'warning' ? 'warning' : 'error');
                        
                    } catch (error) {
                        const errorResult = {
                            status: 'error',
                            message: `测试失败: ${error.message}`,
                            metrics: { '错误': error.message }
                        };
                        testState.results.push({ name: test.name, ...errorResult });
                        
                        const card = createResultCard(test.name, 'error', errorResult.metrics);
                        document.getElementById('results-grid').appendChild(card);
                        
                        log(`❌ ${test.name} 失败: ${error.message}`, 'error');
                    }
                    
                    // 短暂延迟以提供更好的用户体验
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                
                testState.endTime = Date.now();
                const duration = (testState.endTime - testState.startTime) / 1000;
                
                updateProgress(100, '测试完成！');
                
                // 生成总结
                const summary = generateTestSummary();
                const summaryCard = createResultCard('测试总结', summary.status, summary.metrics);
                document.getElementById('results-grid').appendChild(summaryCard);
                
                log(`🎉 所有测试完成！总耗时: ${duration.toFixed(2)} 秒`, 'success');
                log(`📊 ${summary.metrics['通过']} 通过, ${summary.metrics['警告']} 警告, ${summary.metrics['失败']} 失败`, 'info');
                
            } catch (error) {
                log(`💥 测试执行过程中发生严重错误: ${error.message}`, 'error');
            } finally {
                testState.isRunning = false;
            }
        }

        // 生成测试总结
        function generateTestSummary() {
            const passed = testState.results.filter(r => r.status === 'success').length;
            const warned = testState.results.filter(r => r.status === 'warning').length;
            const failed = testState.results.filter(r => r.status === 'error').length;
            const total = testState.results.length;
            
            const passRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
            const status = passed === total ? 'success' : failed > total * 0.2 ? 'error' : 'warning';
            
            return {
                status,
                metrics: {
                    '总测试数': total,
                    '通过': passed,
                    '警告': warned,
                    '失败': failed,
                    '通过率': `${passRate}%`,
                    '执行时间': `${((testState.endTime - testState.startTime) / 1000).toFixed(2)}s`
                }
            };
        }

        // 具体测试函数实现
        async function testSystemInitialization() {
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const checks = {
                '模块容器': !!window.moduleContainer,
                '缓存管理器': !!(window.cacheManager || (window.moduleContainer && window.moduleContainer.has('cacheManager'))),
                '应用实例': !!(window.app || (window.moduleContainer && window.moduleContainer.has('app'))),
                '错误处理器': !!window.errorHandler
            };
            
            const passedChecks = Object.values(checks).filter(Boolean).length;
            const totalChecks = Object.keys(checks).length;
            
            return {
                status: passedChecks === totalChecks ? 'success' : passedChecks >= totalChecks * 0.75 ? 'warning' : 'error',
                message: `系统初始化检查: ${passedChecks}/${totalChecks} 通过`,
                metrics: {
                    '检查项目': totalChecks,
                    '通过项目': passedChecks,
                    '初始化率': `${Math.round((passedChecks / totalChecks) * 100)}%`
                }
            };
        }

        async function testModuleContainerArchitecture() {
            await new Promise(resolve => setTimeout(resolve, 100));
            
            if (!window.moduleContainer) {
                return {
                    status: 'error',
                    message: '模块容器不存在',
                    metrics: { '状态': '未找到', '模块数': 0, '工厂数': 0 }
                };
            }
            
            try {
                await window.moduleContainer.initialize();
                
                const moduleCount = window.moduleContainer.modules ? window.moduleContainer.modules.size : 0;
                const factoryCount = window.moduleContainer.factories ? window.moduleContainer.factories.size : 0;
                const dependencyErrors = window.moduleContainer.validateDependencies().length;
                
                return {
                    status: dependencyErrors === 0 ? 'success' : dependencyErrors <= 2 ? 'warning' : 'error',
                    message: `模块容器架构检查完成`,
                    metrics: {
                        '模块数量': moduleCount,
                        '工厂数量': factoryCount,
                        '依赖错误': dependencyErrors,
                        '架构状态': dependencyErrors === 0 ? '健康' : '有警告'
                    }
                };
            } catch (error) {
                return {
                    status: 'error',
                    message: `架构检查失败: ${error.message}`,
                    metrics: { '错误': error.message }
                };
            }
        }

        async function testDependencyInjectionMechanism() {
            await new Promise(resolve => setTimeout(resolve, 150));
            
            const injectionTests = [];
            
            try {
                if (window.moduleContainer) {
                    // 测试核心模块依赖注入
                    const coreModules = ['config', 'channelDetector', 'fieldMapper', 'gemini'];
                    
                    for (const moduleName of coreModules) {
                        try {
                            if (window.moduleContainer.has(moduleName)) {
                                const instance = window.moduleContainer.get(moduleName);
                                injectionTests.push({ module: moduleName, injected: !!instance });
                            } else {
                                injectionTests.push({ module: moduleName, injected: false });
                            }
                        } catch (error) {
                            injectionTests.push({ module: moduleName, injected: false, error: error.message });
                        }
                    }
                }
                
                const successfulInjections = injectionTests.filter(t => t.injected).length;
                const totalModules = injectionTests.length;
                
                return {
                    status: successfulInjections >= totalModules * 0.8 ? 'success' : successfulInjections > 0 ? 'warning' : 'error',
                    message: `依赖注入机制检查完成`,
                    metrics: {
                        '测试模块': totalModules,
                        '成功注入': successfulInjections,
                        '注入率': `${Math.round((successfulInjections / totalModules) * 100)}%`,
                        '机制状态': successfulInjections >= totalModules * 0.8 ? '正常' : '部分失效'
                    }
                };
                
            } catch (error) {
                return {
                    status: 'error',
                    message: `依赖注入测试失败: ${error.message}`,
                    metrics: { '错误': error.message }
                };
            }
        }

        async function testCacheSystemPerformance() {
            await new Promise(resolve => setTimeout(resolve, 200));
            
            let cacheManager = null;
            try {
                if (window.moduleContainer && window.moduleContainer.has('cacheManager')) {
                    cacheManager = window.moduleContainer.get('cacheManager');
                } else if (window.cacheManager) {
                    cacheManager = window.cacheManager;
                }
                
                if (!cacheManager) {
                    return {
                        status: 'warning',
                        message: '缓存系统未启用',
                        metrics: { '状态': '未找到', '启用': '否', '性能提升': 'N/A' }
                    };
                }
                
                // 执行缓存性能测试
                const testKey = 'performance-test-' + Date.now();
                const testValue = { data: 'test-data', timestamp: Date.now() };
                
                // 写入测试
                const writeStart = performance.now();
                await cacheManager.set(testKey, testValue, 60000);
                const writeTime = performance.now() - writeStart;
                
                // 读取测试
                const readStart = performance.now();
                const retrievedValue = await cacheManager.get(testKey);
                const readTime = performance.now() - readStart;
                
                // 获取统计信息
                const stats = cacheManager.getStats();
                
                return {
                    status: writeTime < 10 && readTime < 5 ? 'success' : 'warning',
                    message: '缓存系统性能检查完成',
                    metrics: {
                        '写入时间': `${writeTime.toFixed(2)}ms`,
                        '读取时间': `${readTime.toFixed(2)}ms`,
                        '命中率': stats.hitRate || '0%',
                        '缓存大小': `${stats.bytesStoredMB || 0}MB`,
                        '系统状态': writeTime < 10 && readTime < 5 ? '优秀' : '良好'
                    }
                };
                
            } catch (error) {
                return {
                    status: 'error',
                    message: `缓存性能测试失败: ${error.message}`,
                    metrics: { '错误': error.message }
                };
            }
        }

        async function testCoreFunctionality() {
            await new Promise(resolve => setTimeout(resolve, 300));
            
            const functionTests = [];
            
            // 测试渠道检测
            try {
                let channelDetector = null;
                if (window.moduleContainer && window.moduleContainer.has('channelDetector')) {
                    channelDetector = window.moduleContainer.get('channelDetector');
                } else if (window.channelDetector) {
                    channelDetector = window.channelDetector;
                }
                
                if (channelDetector) {
                    const result = channelDetector.detectChannel('订单编号：1234567890123456789 飞猪平台');
                    functionTests.push({ 
                        name: '渠道检测', 
                        working: result && result.channel,
                        details: result.channel || '未检测到'
                    });
                } else {
                    functionTests.push({ name: '渠道检测', working: false, details: '模块未找到' });
                }
            } catch (error) {
                functionTests.push({ name: '渠道检测', working: false, details: error.message });
            }
            
            // 测试字段映射
            try {
                let fieldMapper = null;
                if (window.moduleContainer && window.moduleContainer.has('fieldMapper')) {
                    fieldMapper = window.moduleContainer.get('fieldMapper');
                } else if (window.fieldMapper) {
                    fieldMapper = window.fieldMapper;
                }
                
                if (fieldMapper) {
                    const result = await fieldMapper.processCompleteData('客户：张三 电话：13800138000');
                    functionTests.push({ 
                        name: '字段映射', 
                        working: result && result.data,
                        details: result.data ? Object.keys(result.data).length + '个字段' : '无数据'
                    });
                } else {
                    functionTests.push({ name: '字段映射', working: false, details: '模块未找到' });
                }
            } catch (error) {
                functionTests.push({ name: '字段映射', working: false, details: error.message });
            }
            
            // 测试Gemini集成
            try {
                let gemini = null;
                if (window.moduleContainer && window.moduleContainer.has('gemini')) {
                    gemini = window.moduleContainer.get('gemini');
                } else if (window.geminiConfig) {
                    gemini = window.geminiConfig;
                }
                
                if (gemini) {
                    const hasApiKey = gemini.isApiKeyValid && gemini.isApiKeyValid();
                    functionTests.push({ 
                        name: 'Gemini集成', 
                        working: hasApiKey,
                        details: hasApiKey ? 'API密钥有效' : '需要配置'
                    });
                } else {
                    functionTests.push({ name: 'Gemini集成', working: false, details: '模块未找到' });
                }
            } catch (error) {
                functionTests.push({ name: 'Gemini集成', working: false, details: error.message });
            }
            
            const workingFunctions = functionTests.filter(t => t.working).length;
            const totalFunctions = functionTests.length;
            
            return {
                status: workingFunctions === totalFunctions ? 'success' : workingFunctions >= totalFunctions * 0.66 ? 'warning' : 'error',
                message: `核心功能验证完成`,
                metrics: {
                    '功能模块': totalFunctions,
                    '工作正常': workingFunctions,
                    '功能率': `${Math.round((workingFunctions / totalFunctions) * 100)}%`,
                    '详细状态': functionTests.map(t => `${t.name}: ${t.working ? '✓' : '✗'}`).join(', ')
                }
            };
        }

        async function testPerformanceBenchmark() {
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const performanceResults = [];
            
            // 内存使用测试
            const memoryBefore = window.performance && window.performance.memory ? 
                window.performance.memory.usedJSHeapSize : null;
            
            // 执行一些操作以测试性能
            const iterations = 100;
            const startTime = performance.now();
            
            for (let i = 0; i < iterations; i++) {
                // 模拟工作负载
                try {
                    if (window.channelDetector || (window.moduleContainer && window.moduleContainer.has('channelDetector'))) {
                        const detector = window.moduleContainer ? 
                            window.moduleContainer.get('channelDetector') : window.channelDetector;
                        detector.detectChannel(`测试订单${i}: KL-ABC${String(i).padStart(6, '0')}`);
                    }
                } catch (error) {
                    // 忽略错误，继续测试
                }
            }
            
            const endTime = performance.now();
            const avgTime = (endTime - startTime) / iterations;
            
            const memoryAfter = window.performance && window.performance.memory ? 
                window.performance.memory.usedJSHeapSize : null;
            
            const memoryDiff = memoryBefore && memoryAfter ? 
                Math.round((memoryAfter - memoryBefore) / 1024) : null;
            
            return {
                status: avgTime < 5 ? 'success' : avgTime < 15 ? 'warning' : 'error',
                message: '性能基准测试完成',
                metrics: {
                    '迭代次数': iterations,
                    '总耗时': `${(endTime - startTime).toFixed(2)}ms`,
                    '平均时间': `${avgTime.toFixed(2)}ms/次`,
                    '内存变化': memoryDiff ? `${memoryDiff}KB` : 'N/A',
                    '性能等级': avgTime < 5 ? '优秀' : avgTime < 15 ? '良好' : '需优化'
                }
            };
        }

        async function testMemoryUsageAnalysis() {
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const memoryInfo = window.performance && window.performance.memory ? {
                used: Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(window.performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(window.performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : null;
            
            // 统计全局变量
            const globalVars = Object.keys(window).filter(key => 
                key.toLowerCase().includes('channel') || 
                key.toLowerCase().includes('field') || 
                key.toLowerCase().includes('cache') ||
                key.toLowerCase().includes('gemini')
            );
            
            const usage = memoryInfo ? (memoryInfo.used / memoryInfo.limit) * 100 : null;
            
            return {
                status: usage && usage < 50 ? 'success' : usage && usage < 80 ? 'warning' : 'error',
                message: '内存使用分析完成',
                metrics: {
                    '已用内存': memoryInfo ? `${memoryInfo.used}MB` : 'N/A',
                    '总内存': memoryInfo ? `${memoryInfo.total}MB` : 'N/A',
                    '使用率': usage ? `${usage.toFixed(1)}%` : 'N/A',
                    '全局变量': globalVars.length,
                    '内存状态': usage && usage < 50 ? '优秀' : usage && usage < 80 ? '良好' : '需关注'
                }
            };
        }

        async function testErrorHandlingMechanism() {
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const errorTests = [];
            
            // 测试模块容器错误处理
            if (window.moduleContainer) {
                try {
                    window.moduleContainer.get('nonexistent-module');
                } catch (error) {
                    errorTests.push({ test: '模块不存在', handled: error.message.includes('未找到') });
                }
                
                try {
                    const errors = window.moduleContainer.validateDependencies();
                    errorTests.push({ test: '依赖验证', handled: Array.isArray(errors) });
                } catch (error) {
                    errorTests.push({ test: '依赖验证', handled: false });
                }
            }
            
            // 测试全局错误处理器
            if (window.errorHandler) {
                errorTests.push({ test: '全局错误处理器', handled: true });
            } else {
                errorTests.push({ test: '全局错误处理器', handled: false });
            }
            
            const handledErrors = errorTests.filter(t => t.handled).length;
            const totalTests = errorTests.length;
            
            return {
                status: handledErrors >= totalTests * 0.8 ? 'success' : handledErrors > 0 ? 'warning' : 'error',
                message: '错误处理机制检查完成',
                metrics: {
                    '错误测试': totalTests,
                    '正确处理': handledErrors,
                    '处理率': `${Math.round((handledErrors / totalTests) * 100)}%`,
                    '机制状态': handledErrors >= totalTests * 0.8 ? '健壮' : '需加强'
                }
            };
        }

        async function testCompatibilityValidation() {
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const compatibilityChecks = {
                'ES6支持': 'Promise' in window && 'fetch' in window,
                '模块系统': !!window.moduleContainer,
                '传统兼容': !!(window.channelDetector || window.fieldMapper),
                '浏览器API': 'localStorage' in window && 'sessionStorage' in window,
                'Console API': 'console' in window && typeof console.log === 'function'
            };
            
            const passedChecks = Object.values(compatibilityChecks).filter(Boolean).length;
            const totalChecks = Object.keys(compatibilityChecks).length;
            
            return {
                status: passedChecks === totalChecks ? 'success' : passedChecks >= totalChecks * 0.8 ? 'warning' : 'error',
                message: '兼容性验证完成',
                metrics: {
                    '兼容项目': totalChecks,
                    '通过项目': passedChecks,
                    '兼容率': `${Math.round((passedChecks / totalChecks) * 100)}%`,
                    '浏览器': navigator.userAgent.split(' ').pop(),
                    '兼容性': passedChecks === totalChecks ? '完全兼容' : '部分兼容'
                }
            };
        }

        async function testRegressionCheck() {
            await new Promise(resolve => setTimeout(resolve, 200));
            
            const regressionChecks = [];
            
            // 检查关键全局函数是否存在
            const globalFunctions = ['processInput', 'clearInput'];
            globalFunctions.forEach(funcName => {
                regressionChecks.push({
                    name: `全局函数 ${funcName}`,
                    passed: typeof window[funcName] === 'function'
                });
            });
            
            // 检查DOM元素
            const domElements = ['inputContent', 'resultContainer'];
            domElements.forEach(elementId => {
                regressionChecks.push({
                    name: `DOM元素 ${elementId}`,
                    passed: !!document.getElementById(elementId)
                });
            });
            
            // 检查核心模块可用性
            const coreModules = ['channelDetector', 'fieldMapper'];
            coreModules.forEach(moduleName => {
                const available = !!(
                    (window.moduleContainer && window.moduleContainer.has(moduleName)) ||
                    window[moduleName]
                );
                regressionChecks.push({
                    name: `模块 ${moduleName}`,
                    passed: available
                });
            });
            
            const passedChecks = regressionChecks.filter(c => c.passed).length;
            const totalChecks = regressionChecks.length;
            
            return {
                status: passedChecks >= totalChecks * 0.9 ? 'success' : passedChecks >= totalChecks * 0.7 ? 'warning' : 'error',
                message: '回归测试检查完成',
                metrics: {
                    '回归项目': totalChecks,
                    '通过项目': passedChecks,
                    '回归率': `${Math.round((passedChecks / totalChecks) * 100)}%`,
                    '详细结果': regressionChecks.map(c => `${c.name}: ${c.passed ? '✓' : '✗'}`).join(', '),
                    '稳定性': passedChecks >= totalChecks * 0.9 ? '优秀' : '需关注'
                }
            };
        }

        // 清空结果
        function clearResults() {
            document.getElementById('results-grid').innerHTML = '';
            document.getElementById('log-output').innerHTML = '等待测试开始...';
            updateProgress(0, '准备开始测试...');
            testState.results = [];
            log('🧹 测试结果已清空', 'info');
        }

        // 导出报告
        function exportReport() {
            if (testState.results.length === 0) {
                alert('请先运行测试，然后再导出报告');
                return;
            }
            
            const report = {
                metadata: {
                    title: '渠道检测编辑器 - 自动化测试报告',
                    timestamp: new Date().toISOString(),
                    duration: testState.endTime ? (testState.endTime - testState.startTime) / 1000 : 0,
                    testCount: testState.results.length
                },
                summary: generateTestSummary(),
                detailedResults: testState.results,
                environment: {
                    userAgent: navigator.userAgent,
                    viewport: { width: window.innerWidth, height: window.innerHeight },
                    performance: window.performance && window.performance.memory ? {
                        usedJSHeapSize: window.performance.memory.usedJSHeapSize,
                        totalJSHeapSize: window.performance.memory.totalJSHeapSize,
                        jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
                    } : null
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `automated-test-report-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📄 自动化测试报告已导出', 'success');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', async function() {
            log('🔧 自动化测试执行器已就绪', 'success');
            
            try {
                if (window.moduleContainer) {
                    await window.moduleContainer.initialize();
                    log('📦 模块容器初始化完成', 'success');
                }
            } catch (error) {
                log(`⚠️ 模块容器初始化警告: ${error.message}`, 'warning');
            }
            
            log('💡 点击"开始自动化测试"按钮开始验证系统质量', 'info');
        });
    </script>
</body>
</html>