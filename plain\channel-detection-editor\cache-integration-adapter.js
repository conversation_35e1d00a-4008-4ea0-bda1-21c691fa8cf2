/**
 * 缓存集成适配器 - 无缝集成缓存到现有模块
 * 
 * === 设计目标 ===
 * - 零侵入性：不修改现有模块代码的前提下添加缓存
 * - 智能代理：透明地拦截和缓存方法调用
 * - 策略化配置：为不同模块和方法配置不同缓存策略
 * - 性能监控：实时监控缓存效果和性能提升
 * 
 * === 集成模块 ===
 * - FieldMapper.enhanceWithGemini() - Gemini API调用缓存
 * - AddressTranslator.translateAddress() - 地址翻译缓存
 * - ChannelDetector.detectChannel() - 渠道检测缓存
 * 
 * === 缓存策略 ===
 * - Gemini API: 10分钟TTL，高优先级，压缩存储
 * - 地址翻译: 30分钟TTL，中高优先级，本地存储
 * - 渠道检测: 5分钟TTL，中优先级，内存存储
 * 
 * @INTEGRATION 缓存系统集成核心
 * @PERFORMANCE 性能优化代理层
 * @MONITORING 缓存效果监控
 */

class CacheIntegrationAdapter {
    constructor(cacheManager, options = {}) {
        if (!cacheManager) {
            throw new Error("CacheManager is a required dependency for CacheIntegrationAdapter.");
        }
        this.cacheManager = cacheManager;
        this.options = {
            // 全局开关
            enabled: true,
            
            // 调试模式
            debugMode: false,
            
            // 性能监控
            enablePerfMonitoring: true,
            
            // 缓存键前缀
            keyPrefix: 'CDE',
            
            // 默认策略
            defaultStrategy: 'default',
            
            ...options
        };
        
        // 性能监控数据
        this.performanceStats = {
            gemini: { calls: 0, cacheHits: 0, avgTime: 0, timeSaved: 0 },
            address: { calls: 0, cacheHits: 0, avgTime: 0, timeSaved: 0 },
            channel: { calls: 0, cacheHits: 0, avgTime: 0, timeSaved: 0 }
        };
        
        // 代理的方法映射
        this.proxiedMethods = new Map();
        
        console.log('🔌 缓存集成适配器已创建 (Refactored)');
    }
    
    /**
     * 为FieldMapper添加Gemini API缓存
     * @INTEGRATION 集成Gemini API调用缓存
     * @param {FieldMapper} fieldMapper - 字段映射器实例
     * @returns {FieldMapper} 代理后的实例
     */
    enhanceFieldMapper(fieldMapper) {
        if (!this.options.enabled || !fieldMapper) {
            return fieldMapper;
        }
        
        console.log('🚀 为FieldMapper添加缓存增强...');
        
        // 代理enhanceWithGemini方法
        const originalEnhanceWithGemini = fieldMapper.enhanceWithGemini.bind(fieldMapper);
        
        fieldMapper.enhanceWithGemini = this.createCachedMethod(
            originalEnhanceWithGemini,
            {
                cacheKey: (text, localExtraction) => this.generateGeminiCacheKey(text, localExtraction),
                strategy: 'gemini',
                methodName: 'enhanceWithGemini',
                moduleType: 'gemini'
            }
        );
        
        // 代理processCompleteData方法（可选，用于更高级的缓存）
        const originalProcessCompleteData = fieldMapper.processCompleteData.bind(fieldMapper);
        
        fieldMapper.processCompleteData = this.createCachedMethod(
            originalProcessCompleteData,
            {
                cacheKey: (text) => this.generateProcessingCacheKey(text),
                strategy: 'gemini',
                methodName: 'processCompleteData',
                moduleType: 'gemini',
                // 完整处理结果缓存时间稍短
                ttl: 5 * 60 * 1000 // 5分钟
            }
        );
        
        this.proxiedMethods.set('fieldMapper', ['enhanceWithGemini', 'processCompleteData']);
        console.log('✅ FieldMapper缓存增强完成');
        
        return fieldMapper;
    }
    
    /**
     * 为AddressTranslator添加地址翻译缓存
     * @INTEGRATION 集成地址翻译缓存
     * @param {AddressTranslator} addressTranslator - 地址翻译器实例
     * @returns {AddressTranslator} 代理后的实例
     */
    enhanceAddressTranslator(addressTranslator) {
        if (!this.options.enabled || !addressTranslator) {
            return addressTranslator;
        }
        
        console.log('🌐 为AddressTranslator添加缓存增强...');
        
        // 代理translateAddress方法
        const originalTranslateAddress = addressTranslator.translateAddress.bind(addressTranslator);
        
        addressTranslator.translateAddress = this.createCachedMethod(
            originalTranslateAddress,
            {
                cacheKey: (address, options = {}) => this.generateAddressCacheKey(address, options),
                strategy: 'address',
                methodName: 'translateAddress',
                moduleType: 'address'
            }
        );
        
        // 代理translateHotel方法
        const originalTranslateHotel = addressTranslator.translateHotel.bind(addressTranslator);
        
        addressTranslator.translateHotel = this.createCachedMethod(
            originalTranslateHotel,
            {
                cacheKey: (address) => this.generateHotelCacheKey(address),
                strategy: 'address',
                methodName: 'translateHotel',
                moduleType: 'address'
            }
        );
        
        // 代理translateAirport方法
        const originalTranslateAirport = addressTranslator.translateAirport.bind(addressTranslator);
        
        addressTranslator.translateAirport = this.createCachedMethod(
            originalTranslateAirport,
            {
                cacheKey: (address) => this.generateAirportCacheKey(address),
                strategy: 'address',
                methodName: 'translateAirport',
                moduleType: 'address'
            }
        );
        
        this.proxiedMethods.set('addressTranslator', ['translateAddress', 'translateHotel', 'translateAirport']);
        console.log('✅ AddressTranslator缓存增强完成');
        
        return addressTranslator;
    }
    
    /**
     * 为ChannelDetector添加渠道检测缓存
     * @INTEGRATION 集成渠道检测缓存
     * @param {ChannelDetector} channelDetector - 渠道检测器实例
     * @returns {ChannelDetector} 代理后的实例
     */
    enhanceChannelDetector(channelDetector) {
        if (!this.options.enabled || !channelDetector) {
            return channelDetector;
        }
        
        console.log('🔍 为ChannelDetector添加缓存增强...');
        
        // 代理detectChannel方法
        const originalDetectChannel = channelDetector.detectChannel.bind(channelDetector);
        
        channelDetector.detectChannel = this.createCachedMethod(
            originalDetectChannel,
            {
                cacheKey: (input) => this.generateChannelCacheKey(input),
                strategy: 'channel',
                methodName: 'detectChannel',
                moduleType: 'channel'
            }
        );
        
        this.proxiedMethods.set('channelDetector', ['detectChannel']);
        console.log('✅ ChannelDetector缓存增强完成');
        
        return channelDetector;
    }
    
    /**
     * 创建缓存代理方法
     * @CORE 缓存代理方法工厂
     * @param {Function} originalMethod - 原始方法
     * @param {Object} config - 缓存配置
     * @returns {Function} 代理后的方法
     */
    createCachedMethod(originalMethod, config) {
        return async (...args) => {
            const startTime = Date.now();
            
            try {
                // 生成缓存键
                const cacheKey = typeof config.cacheKey === 'function' 
                    ? config.cacheKey(...args)
                    : this.generateDefaultCacheKey(config.methodName, args);
                
                if (this.options.debugMode) {
                    console.log(`🔍 检查缓存 [${config.moduleType}]:`, { method: config.methodName, key: cacheKey });
                }
                
                // 尝试从缓存获取
                const cachedResult = await this.cacheManager.get(cacheKey, config.strategy);
                
                if (cachedResult !== null) {
                    // 缓存命中
                    const responseTime = Date.now() - startTime;
                    this.updatePerformanceStats(config.moduleType, true, responseTime, 0);
                    
                    if (this.options.debugMode) {
                        console.log(`🎯 缓存命中 [${config.moduleType}]:`, { 
                            method: config.methodName, 
                            responseTime: `${responseTime}ms` 
                        });
                    }
                    
                    return cachedResult;
                }
                
                // 缓存未命中，调用原始方法
                const methodStartTime = Date.now();
                const result = await originalMethod(...args);
                const methodTime = Date.now() - methodStartTime;
                
                // 将结果存入缓存
                if (result !== null && result !== undefined) {
                    const ttl = config.ttl || null;
                    await this.cacheManager.set(cacheKey, result, config.strategy, ttl);
                    
                    if (this.options.debugMode) {
                        console.log(`💾 结果已缓存 [${config.moduleType}]:`, { 
                            method: config.methodName,
                            strategy: config.strategy,
                            ttl: ttl
                        });
                    }
                }
                
                // 更新性能统计
                const totalTime = Date.now() - startTime;
                this.updatePerformanceStats(config.moduleType, false, totalTime, methodTime);
                
                return result;
                
            } catch (error) {
                console.error(`❌ 缓存代理方法执行失败 [${config.moduleType}]:`, error);
                
                // 降级到原始方法
                try {
                    return await originalMethod(...args);
                } catch (fallbackError) {
                    console.error(`❌ 原始方法也失败了 [${config.moduleType}]:`, fallbackError);
                    throw fallbackError;
                }
            }
        };
    }
    
    /**
     * 生成Gemini API缓存键
     * @UTIL 为Gemini API调用生成唯一缓存键
     */
    generateGeminiCacheKey(text, localExtraction) {
        // 基于输入文本和本地提取结果生成键
        const textHash = this.hashString(text.trim());
        const extractionHash = this.hashString(JSON.stringify(localExtraction || {}));
        return `${this.options.keyPrefix}:gemini:${textHash}:${extractionHash}`;
    }
    
    /**
     * 生成完整处理缓存键
     * @UTIL 为完整处理流程生成缓存键
     */
    generateProcessingCacheKey(text) {
        const textHash = this.hashString(text.trim());
        return `${this.options.keyPrefix}:processing:${textHash}`;
    }
    
    /**
     * 生成地址翻译缓存键
     * @UTIL 为地址翻译生成唯一缓存键
     */
    generateAddressCacheKey(address, options = {}) {
        const addressHash = this.hashString(address.trim().toLowerCase());
        const optionsHash = this.hashString(JSON.stringify(options));
        return `${this.options.keyPrefix}:address:${addressHash}:${optionsHash}`;
    }
    
    /**
     * 生成酒店翻译缓存键
     * @UTIL 为酒店翻译生成缓存键
     */
    generateHotelCacheKey(address) {
        const addressHash = this.hashString(address.trim().toLowerCase());
        return `${this.options.keyPrefix}:hotel:${addressHash}`;
    }
    
    /**
     * 生成机场翻译缓存键
     * @UTIL 为机场翻译生成缓存键
     */
    generateAirportCacheKey(address) {
        const addressHash = this.hashString(address.trim().toLowerCase());
        return `${this.options.keyPrefix}:airport:${addressHash}`;
    }
    
    /**
     * 生成渠道检测缓存键
     * @UTIL 为渠道检测生成唯一缓存键
     */
    generateChannelCacheKey(input) {
        if (!input || typeof input !== 'string') {
            return `${this.options.keyPrefix}:channel:invalid`;
        }
        
        // 提取关键特征而非完整内容（隐私保护）
        const features = this.extractChannelFeatures(input);
        const featuresHash = this.hashString(JSON.stringify(features));
        return `${this.options.keyPrefix}:channel:${featuresHash}`;
    }
    
    /**
     * 生成默认缓存键
     * @UTIL 为未配置特殊键生成器的方法生成默认键
     */
    generateDefaultCacheKey(methodName, args) {
        const argsHash = this.hashString(JSON.stringify(args));
        return `${this.options.keyPrefix}:${methodName}:${argsHash}`;
    }
    
    /**
     * 提取渠道检测特征（隐私保护）
     * @PRIVACY 提取关键特征而非完整内容
     */
    extractChannelFeatures(input) {
        const features = {
            length: input.length,
            hasOrderNumber: /订单编号|订购编号|order.*number/i.test(input),
            hasReferenceNumber: /\b[A-Z]{2}[A-Z0-9]{6,12}\b/.test(input),
            hasPhoneNumber: /\+?\d{1,3}[-.\s]?\d{3,4}[-.\s]?\d{4,8}/.test(input),
            hasEmailAddress: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/.test(input),
            hasFlightInfo: /航班|flight|MH\d+|CX\d+|SQ\d+/i.test(input),
            hasDateInfo: /\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}[-/]\d{1,2}[-/]\d{4}/.test(input),
            keywordHints: this.extractKeywordHints(input)
        };
        
        return features;
    }
    
    /**
     * 提取关键词提示
     * @PRIVACY 提取渠道关键词而非具体内容
     */
    extractKeywordHints(input) {
        const keywords = [];
        const lowerInput = input.toLowerCase();
        
        const channelKeywords = ['fliggy', 'klook', 'kkday', 'ctrip', 'trip', 'jingge'];
        for (const keyword of channelKeywords) {
            if (lowerInput.includes(keyword)) {
                keywords.push(keyword);
            }
        }
        
        return keywords;
    }
    
    /**
     * 字符串哈希函数
     * @UTIL 生成字符串的哈希值
     */
    hashString(str) {
        if (!str) return '0';
        
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return Math.abs(hash).toString(16);
    }
    
    /**
     * 更新性能统计
     * @MONITORING 更新模块性能指标
     */
    updatePerformanceStats(moduleType, isHit, responseTime, methodTime = 0) {
        if (!this.options.enablePerfMonitoring) return;
        
        const stats = this.performanceStats[moduleType];
        if (!stats) return;
        
        stats.calls++;
        
        if (isHit) {
            stats.cacheHits++;
            stats.timeSaved += Math.max(0, methodTime - responseTime);
        } else {
            // 更新平均执行时间（指数移动平均）
            if (stats.avgTime === 0) {
                stats.avgTime = methodTime;
            } else {
                stats.avgTime = stats.avgTime * 0.9 + methodTime * 0.1;
            }
        }
    }
    
    /**
     * 获取性能统计报告
     * @MONITORING 生成详细的性能统计报告
     * @returns {Object} 性能统计报告
     */
    getPerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            overall: {
                totalCalls: 0,
                totalCacheHits: 0,
                overallHitRate: '0.00%',
                totalTimeSaved: 0
            },
            byModule: {}
        };
        
        for (const [moduleType, stats] of Object.entries(this.performanceStats)) {
            const hitRate = stats.calls > 0 ? ((stats.cacheHits / stats.calls) * 100).toFixed(2) : '0.00';
            
            report.byModule[moduleType] = {
                calls: stats.calls,
                cacheHits: stats.cacheHits,
                hitRate: `${hitRate}%`,
                avgResponseTime: `${stats.avgTime.toFixed(2)}ms`,
                timeSaved: `${stats.timeSaved.toFixed(2)}ms`
            };
            
            report.overall.totalCalls += stats.calls;
            report.overall.totalCacheHits += stats.cacheHits;
            report.overall.totalTimeSaved += stats.timeSaved;
        }
        
        // 计算总体命中率
        if (report.overall.totalCalls > 0) {
            const overallHitRate = (report.overall.totalCacheHits / report.overall.totalCalls * 100).toFixed(2);
            report.overall.overallHitRate = `${overallHitRate}%`;
        }
        
        report.overall.totalTimeSaved = `${report.overall.totalTimeSaved.toFixed(2)}ms`;
        
        return report;
    }
    
    /**
     * 重置性能统计
     * @MONITORING 清零性能统计数据
     */
    resetPerformanceStats() {
        for (const moduleType of Object.keys(this.performanceStats)) {
            this.performanceStats[moduleType] = {
                calls: 0,
                cacheHits: 0,
                avgTime: 0,
                timeSaved: 0
            };
        }
        
        console.log('📊 性能统计已重置');
    }
    
    /**
     * 启用缓存
     * @CONTROL 启用缓存功能
     */
    enable() {
        this.options.enabled = true;
        console.log('✅ 缓存功能已启用');
    }
    
    /**
     * 禁用缓存
     * @CONTROL 禁用缓存功能
     */
    disable() {
        this.options.enabled = false;
        console.log('⏸️ 缓存功能已禁用');
    }
    
    /**
     * 启用调试模式
     * @DEBUG 启用详细的调试日志
     */
    enableDebugMode() {
        this.options.debugMode = true;
        console.log('🐛 缓存调试模式已启用');
    }
    
    /**
     * 禁用调试模式
     * @DEBUG 禁用调试日志
     */
    disableDebugMode() {
        this.options.debugMode = false;
        console.log('🔇 缓存调试模式已禁用');
    }
    
    /**
     * 预热常用缓存
     * @OPTIMIZATION 预加载常用的缓存数据
     * @param {Array} warmupData - 预热数据列表
     * @returns {Promise<Object>} 预热结果
     */
    async warmupCache(warmupData = []) {
        console.log('🔥 开始缓存预热...', { count: warmupData.length });
        
        const results = {
            total: warmupData.length,
            successful: 0,
            failed: 0,
            errors: []
        };
        
        for (const item of warmupData) {
            try {
                const { key, value, strategy = 'default', ttl = null } = item;
                const success = await this.cacheManager.set(key, value, strategy, ttl);
                
                if (success) {
                    results.successful++;
                } else {
                    results.failed++;
                }
            } catch (error) {
                results.failed++;
                results.errors.push({
                    item: item,
                    error: error.message
                });
                console.warn('⚠️ 预热项目失败:', error.message);
            }
        }
        
        console.log('✅ 缓存预热完成:', results);
        return results;
    }
    
    /**
     * 清理指定模块的缓存
     * @MAINTENANCE 清理特定模块的所有缓存
     * @param {string} moduleType - 模块类型 
     * @returns {Promise<boolean>} 是否清理成功
     */
    async clearModuleCache(moduleType) {
        if (!moduleType) {
            console.error('❌ 模块类型不能为空');
            return false;
        }
        
        console.log(`🧹 清理模块缓存 [${moduleType}]...`);
        
        try {
            // 根据模块类型选择对应的缓存策略
            const strategyMap = {
                'gemini': 'gemini',
                'address': 'address', 
                'channel': 'channel'
            };
            
            const strategy = strategyMap[moduleType] || 'default';
            const success = await this.cacheManager.clear(strategy);
            
            if (success) {
                // 重置该模块的性能统计
                if (this.performanceStats[moduleType]) {
                    this.performanceStats[moduleType] = {
                        calls: 0,
                        cacheHits: 0,
                        avgTime: 0,
                        timeSaved: 0
                    };
                }
                
                console.log(`✅ 模块缓存清理完成 [${moduleType}]`);
                return true;
            } else {
                console.warn(`⚠️ 模块缓存清理失败 [${moduleType}]`);
                return false;
            }
            
        } catch (error) {
            console.error(`❌ 模块缓存清理异常 [${moduleType}]:`, error);
            return false;
        }
    }
    
    /**
     * 获取缓存健康状态
     * @MONITORING 获取整体缓存系统健康状态
     * @returns {Promise<Object>} 健康状态报告
     */
    async getCacheHealthStatus() {
        try {
            const cacheHealth = await this.cacheManager.getHealthStatus();
            const cacheStats = this.cacheManager.getStats();
            const perfReport = this.getPerformanceReport();
            
            return {
                timestamp: new Date().toISOString(),
                enabled: this.options.enabled,
                cacheHealth: cacheHealth,
                cacheStats: cacheStats,
                performanceReport: perfReport,
                proxiedMethods: Object.fromEntries(this.proxiedMethods)
            };
        } catch (error) {
            return {
                timestamp: new Date().toISOString(),
                enabled: this.options.enabled,
                error: error.message,
                status: 'unhealthy'
            };
        }
    }
}

// 模块工厂函数
function createCacheIntegrationAdapterModule(container) {
    const cacheManager = container.get('cacheManager');
    return new CacheIntegrationAdapter(cacheManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('cacheIntegrationAdapter', createCacheIntegrationAdapterModule, ['cacheManager']);
    console.log('📦 CacheIntegrationAdapter已注册到模块容器');
}