/**
 * 模块容器 - 依赖注入系统
 * 
 * 设计原则：
 * 1. 消除全局变量污染
 * 2. 实现显式依赖注入  
 * 3. 支持模块化测试
 * 4. 保持向后兼容性
 * 
 * 作者：Linus (代码品味优化)
 */
class ModuleContainer {
    constructor() {
        this.modules = new Map();
        this.factories = new Map();
        this.loading = new Set();
        this.initialized = false;
    }
    
    /**
     * 注册模块工厂函数
     * @param {string} name - 模块名
     * @param {Function} factory - 工厂函数，接收容器实例作为参数
     * @param {Array} dependencies - 依赖列表
     */
    register(name, factory, dependencies = []) {
        if (typeof factory !== 'function') {
            throw new Error(`模块 ${name} 的工厂必须是函数`);
        }
        
        this.factories.set(name, {
            factory,
            dependencies,
            singleton: true
        });
        
        console.log(`📦 注册模块: ${name}, 依赖: [${dependencies.join(', ')}]`);
    }
    
    /**
     * 获取模块实例
     * @param {string} name - 模块名
     * @returns {any} 模块实例
     */
    get(name) {
        if (this.loading.has(name)) {
            throw new Error(`检测到循环依赖: ${name}`);
        }
        
        // 已存在的实例直接返回
        if (this.modules.has(name)) {
            return this.modules.get(name);
        }
        
        const moduleConfig = this.factories.get(name);
        if (!moduleConfig) {
            throw new Error(`未找到模块: ${name}`);
        }
        
        this.loading.add(name);
        
        try {
            // 递归解析依赖
            const dependencies = moduleConfig.dependencies.map(dep => this.get(dep));
            
            // 创建模块实例
            const instance = moduleConfig.factory(this, ...dependencies);
            
            if (moduleConfig.singleton) {
                this.modules.set(name, instance);
            }
            
            console.log(`✅ 创建模块实例: ${name}`);
            return instance;
            
        } finally {
            this.loading.delete(name);
        }
    }
    
    /**
     * 检查模块是否存在
     * @param {string} name - 模块名
     * @returns {boolean}
     */
    has(name) {
        return this.factories.has(name);
    }
    
    /**
     * 初始化所有已注册的模块
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initialized) {
            console.log('⚠️  容器已初始化，跳过重复初始化');
            return;
        }
        
        console.log('🚀 初始化模块容器...');
        
        // 获取所有模块来触发初始化
        for (const [name] of this.factories) {
            try {
                this.get(name);
            } catch (error) {
                console.error(`❌ 模块 ${name} 初始化失败:`, error);
                throw error;
            }
        }
        
        this.initialized = true;
        console.log('✅ 模块容器初始化完成');
    }
    
    /**
     * 清理容器
     */
    dispose() {
        // 调用模块的dispose方法（如果存在）
        for (const [name, instance] of this.modules) {
            if (instance && typeof instance.dispose === 'function') {
                try {
                    instance.dispose();
                    console.log(`🗑️  清理模块: ${name}`);
                } catch (error) {
                    console.error(`清理模块 ${name} 失败:`, error);
                }
            }
        }
        
        this.modules.clear();
        this.factories.clear();
        this.loading.clear();
        this.initialized = false;
        
        console.log('🧹 容器已清理');
    }
    
    /**
     * 获取模块依赖图
     * @returns {Object} 依赖关系图
     */
    getDependencyGraph() {
        const graph = {};
        for (const [name, config] of this.factories) {
            graph[name] = config.dependencies;
        }
        return graph;
    }
    
    /**
     * 验证依赖关系
     * @returns {Array} 错误列表
     */
    validateDependencies() {
        const errors = [];
        
        for (const [name, config] of this.factories) {
            for (const dep of config.dependencies) {
                if (!this.factories.has(dep)) {
                    errors.push(`模块 ${name} 依赖未找到: ${dep}`);
                }
            }
        }
        
        // 简单循环依赖检测
        const visited = new Set();
        const visiting = new Set();
        
        const checkCycle = (name) => {
            if (visiting.has(name)) {
                errors.push(`检测到循环依赖: ${name}`);
                return;
            }
            
            if (visited.has(name)) return;
            
            visiting.add(name);
            const config = this.factories.get(name);
            
            if (config) {
                for (const dep of config.dependencies) {
                    checkCycle(dep);
                }
            }
            
            visiting.delete(name);
            visited.add(name);
        };
        
        for (const [name] of this.factories) {
            checkCycle(name);
        }
        
        return errors;
    }
}

// 创建全局容器实例（兼容性）
window.moduleContainer = new ModuleContainer();

// 提供便捷的全局函数
window.registerModule = (name, factory, deps) => {
    window.moduleContainer.register(name, factory, deps);
};

window.getModule = (name) => {
    return window.moduleContainer.get(name);
};

/**
 * 统一服务获取工具 - 支持模块容器和全局变量回退
 * @param {string} serviceName - 服务名称
 * @returns {Object|null} 服务实例
 */
window.getService = function(serviceName) {
    // 优先从模块容器获取
    if (window.moduleContainer && window.moduleContainer.has && window.moduleContainer.has(serviceName)) {
        try {
            return window.moduleContainer.get(serviceName);
        } catch (error) {
            console.warn(`从模块容器获取${serviceName}服务失败:`, error);
        }
    }

    // 向后兼容：根据服务名映射到全局变量
    const serviceMap = {
        'gemini': 'geminiConfig',
        'promptFragmentManager': 'promptFragmentManager',
        'configManager': 'configManager',
        'localStorageManager': 'localStorageManager'
    };

    const globalName = serviceMap[serviceName] || serviceName;
    if (window[globalName]) {
        return window[globalName];
    }

    console.warn(`服务 ${serviceName} 不可用`);
    return null;
};

console.log('🏗️  模块容器系统已加载');