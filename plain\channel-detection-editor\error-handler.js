/**
 * 错误处理模块 - 全局错误捕获和降级方案
 * 
 * === 文件依赖关系网络 ===
 * 依赖项：无直接依赖（使用浏览器原生事件监听）
 * 被依赖：所有模块（全局错误处理）
 * 全局变量：创建 window.errorHandler 实例和全局工具函数
 * 监听类型：global error, unhandledrejection, console.error
 * 
 * === 核心功能 ===
 * - 全局错误捕获和分类处理
 * - 用户友好的错误通知显示
 * - 错误日志记录和存储
 * - 安全执行包装器（safeExecute）
 * - 降级处理机制（withFallback）
 * 
 * === 集成点 ===
 * - 为所有模块提供全局错误处理服务
 * - gemini-config.js: API调用失败时显示降级提示
 * - field-mapper.js: 字段处理错误降级到本地模式
 * - local-storage-manager.js: 存储失败降级处理
 * 
 * === 使用场景 ===
 * - 全局错误监控和统一处理
 * - API调用失败的降级方案
 * - 用户友好的错误反馈
 * - 开发调试和日志记录
 * 
 * === 注意事项 ===
 * 该模块应在所有其他模块之前加载，确保全局错误能被捕获
 * 错误日志会保存到localStorage，最多100条记录
 * 支持自定义通知样式和自动消失
 */

// 错误处理和降级方案

class ErrorHandler {
    constructor() {
        this.initialize();
    }

    initialize() {
        console.log('错误处理器已初始化');
        // 重入锁与节流状态，防止递归与刷屏 @LIFECYCLE
        this._handling = false; // 重入锁
        this._lastLog = { msg: '', ts: 0, count: 0 }; // 简易节流（相同消息短时间内合并）

        // 全局错误捕获
        this.setupGlobalErrorHandling();

        // 未处理的Promise拒绝
        this.setupPromiseRejectionHandling();
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'global',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        // 重写console.error
        const originalConsoleError = console.error;
        console.error = (...args) => {
            try {
                const msg = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
                // 节流：相同消息在1秒内只处理一次 @LIFECYCLE
                const now = Date.now();
                if (this._lastLog.msg === msg && (now - this._lastLog.ts) < 1000) {
                    this._lastLog.count++;
                } else {
                    this._lastLog = { msg, ts: now, count: 0 };
                    this.handleError({ type: 'console', message: msg, timestamp: new Date().toISOString() });
                }
            } catch {}
            // 始终调用原始 console.error，避免吞日志 @LIFECYCLE
            try { originalConsoleError.apply(console, args); } catch {}
        };
    }

    /**
     * 设置Promise拒绝处理
     */
    setupPromiseRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unknown promise rejection',
                reason: event.reason,
                timestamp: new Date().toISOString()
            });
            
            // 防止默认错误提示
            event.preventDefault();
        });
    }

    /**
     * 处理错误
     */
    handleError(errorInfo) {
        // 重入保护：防止 handleError 内再次触发自己导致递归 @LIFECYCLE
        if (this._handling) return;
        this._handling = true;
        try {
            console.warn('错误捕获:', errorInfo);
            // 根据错误类型采取不同措施
            switch (errorInfo.type) {
                case 'global':
                    this.handleGlobalError(errorInfo);
                    break;
                case 'promise':
                    this.handlePromiseError(errorInfo);
                    break;
                case 'console':
                    this.handleConsoleError(errorInfo);
                    break;
                default:
                    this.handleUnknownError(errorInfo);
            }
            // 记录错误
            this.logError(errorInfo);
        } finally {
            this._handling = false;
        }
    }

    /**
     * 处理全局错误
     */
    handleGlobalError(errorInfo) {
        // 忽略一些常见的无害错误
        if (this.isBenignError(errorInfo)) {
            return;
        }

        // 显示用户友好的错误提示
        this.showUserError('系统发生错误', '请刷新页面重试');
    }

    /**
     * 处理Promise错误
     */
    handlePromiseError(errorInfo) {
        // API请求失败降级处理
        if (errorInfo.message.includes('fetch') || errorInfo.message.includes('API')) {
            this.showUserWarning('网络连接问题', '正在使用本地模式');
            return;
        }

        this.showUserError('操作失败', '请检查输入后重试');
    }

    /**
     * 处理控制台错误
     */
    handleConsoleError(errorInfo) {
        // 通常只是记录，不显示给用户
        console.warn('控制台错误:', errorInfo.message);
    }

    /**
     * 处理未知错误
     */
    handleUnknownError(errorInfo) {
        this.showUserError('未知错误', '请联系技术支持');
    }

    /**
     * 判断是否为无害错误
     */
    isBenignError(errorInfo) {
        const benignPatterns = [
            /Script error/, // 跨域脚本错误
            /ResizeObserver/, // 调整观察者错误
            /^$/, // 空错误信息
        ];

        return benignPatterns.some(pattern => 
            pattern.test(errorInfo.message)
        );
    }

    /**
     * 显示用户错误
     */
    showUserError(title, message) {
        this.showNotification(title, message, 'error');
    }

    /**
     * 显示用户警告
     */
    showUserWarning(title, message) {
        this.showNotification(title, message, 'warning');
    }

    /**
     * 显示用户信息
     */
    showUserInfo(title, message) {
        this.showNotification(title, message, 'info');
    }

    /**
     * 显示通知
     */
    showNotification(title, message, type = 'info') {
        // 移除现有的通知
        this.removeExistingNotifications();

        const notification = document.createElement('div');
        notification.className = `error-notification error-notification-${type}`;
        notification.innerHTML = `
            <div class="error-notification-content">
                <strong>${title}</strong>
                <p>${message}</p>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加样式（如果尚未添加）
        this.ensureNotificationStyles();

        document.body.appendChild(notification);

        // 自动消失
        if (type !== 'error') {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    /**
     * 移除现有通知
     */
    removeExistingNotifications() {
        const existing = document.querySelectorAll('.error-notification');
        existing.forEach(el => el.remove());
    }

    /**
     * 确保通知样式
     */
    ensureNotificationStyles() {
        if (document.getElementById('error-notification-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'error-notification-styles';
        style.textContent = `
            .error-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border-left: 4px solid #007bff;
                animation: slideIn 0.3s ease;
            }

            .error-notification-error {
                border-left-color: #dc3545;
            }

            .error-notification-warning {
                border-left-color: #ffc107;
            }

            .error-notification-info {
                border-left-color: #17a2b8;
            }

            .error-notification-content {
                padding: 16px;
                position: relative;
            }

            .error-notification-content strong {
                display: block;
                margin-bottom: 8px;
                color: #333;
            }

            .error-notification-content p {
                margin: 0;
                color: #666;
                line-height: 1.5;
            }

            .error-notification-content button {
                position: absolute;
                top: 12px;
                right: 12px;
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #999;
            }

            .error-notification-content button:hover {
                color: #333;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 记录错误
     */
    logError(errorInfo) {
        const errorLog = {
            ...errorInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // 保存到localStorage
        this.saveErrorToStorage(errorLog);

        // 控制台输出
        console.group('错误详情');
        console.error('错误时间:', errorLog.timestamp);
        console.error('错误类型:', errorLog.type);
        console.error('错误信息:', errorLog.message);
        if (errorLog.error) console.error('错误对象:', errorLog.error);
        console.groupEnd();
    }

    /**
     * 保存错误到存储
     */
    saveErrorToStorage(errorLog) {
        try {
            const existingLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');
            
            // 只保留最近100条错误
            existingLogs.unshift(errorLog);
            if (existingLogs.length > 100) {
                existingLogs.length = 100;
            }

            localStorage.setItem('error_logs', JSON.stringify(existingLogs));
        } catch (error) {
            console.warn('保存错误日志失败:', error);
        }
    }

    /**
     * 获取错误日志
     */
    getErrorLogs() {
        try {
            return JSON.parse(localStorage.getItem('error_logs') || '[]');
        } catch {
            return [];
        }
    }

    /**
     * 清空错误日志
     */
    clearErrorLogs() {
        localStorage.removeItem('error_logs');
    }

    /**
     * 安全执行函数
     */
    safeExecute(fn, fallbackValue = null, context = null) {
        try {
            return fn.call(context);
        } catch (error) {
            this.handleError({
                type: 'safeExecute',
                message: `安全执行失败: ${error.message}`,
                error: error,
                timestamp: new Date().toISOString()
            });
            return fallbackValue;
        }
    }

    /**
     * 安全异步执行
     */
    async safeExecuteAsync(fn, fallbackValue = null, context = null) {
        try {
            return await fn.call(context);
        } catch (error) {
            this.handleError({
                type: 'safeExecuteAsync',
                message: `安全异步执行失败: ${error.message}`,
                error: error,
                timestamp: new Date().toISOString()
            });
            return fallbackValue;
        }
    }

    /**
     * 降级方案：本地存储回退
     */
    async withFallback(mainAction, fallbackAction, context = null) {
        try {
            return await mainAction.call(context);
        } catch (error) {
            this.handleError({
                type: 'fallback',
                message: `主操作失败，使用降级方案: ${error.message}`,
                error: error,
                timestamp: new Date().toISOString()
            });
            
            try {
                return await fallbackAction.call(context);
            } catch (fallbackError) {
                this.handleError({
                    type: 'fallbackFailed',
                    message: `降级方案也失败: ${fallbackError.message}`,
                    error: fallbackError,
                    timestamp: new Date().toISOString()
                });
                throw fallbackError;
            }
        }
    }
}

// 模块工厂函数
function createErrorHandlerModule() {
    return new ErrorHandler();
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('errorHandler', createErrorHandlerModule, []);
    console.log('📦 ErrorHandler已注册到模块容器');
}