{"version": "2.0.0", "tasks": [{"label": "install-deps", "type": "shell", "command": "npm i", "args": [], "problemMatcher": ["$eslint-stylish"], "group": "build"}, {"label": "install-playwright", "type": "shell", "command": "npm i --no-audit --no-fund --loglevel=error && npx --yes playwright install --with-deps", "args": [], "problemMatcher": [], "group": "build"}, {"label": "run-playwright-tests", "type": "shell", "command": "npx playwright test", "args": [], "isBackground": false, "problemMatcher": [], "group": "test"}]}