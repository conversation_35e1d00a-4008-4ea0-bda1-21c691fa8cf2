/**
 * 渠道检测器模块 - Refactored
 * 
 * 设计原则：
 * - 依赖注入：通过构造函数接收ConfigManager。
 * - 单一职责：专注于渠道检测逻辑。
 * - 可测试性：支持模拟规则配置的单元测试。
 */
class ChannelDetector {
    constructor(configManager) {
        if (!configManager) {
            throw new Error("ConfigManager is a required dependency for ChannelDetector.");
        }
        this.configManager = configManager;
        this.dataUtils = this.configManager.DataUtils;
        this.detectionRules = this.processDetectionRules();
        console.log('渠道检测器已初始化 - Refactored');
    }

    processDetectionRules() {
        const config = {
            channelRules: this.configManager.getChannelDetectionRules(),
            referencePatterns: this.configManager.getReferencePatterns(),
            keywordDetection: this.configManager.getKeywordDetection()
        };

        const rules = {};

        if (config.channelRules) {
            for (const [key, rule] of Object.entries(config.channelRules)) {
                const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
                rules[key] = {
                    patterns: patterns.map(p => {
                        try { return this.dataUtils.patternToRegex(p instanceof RegExp ? p.source : String(p || '')); } catch { return null; }
                    }).filter(Boolean),
                    confidence: typeof rule.confidence === 'number' ? rule.confidence : 0.8,
                    channel: rule.channel || rule.name || key
                };
            }
        }

        rules.referencePatterns = config.referencePatterns || {};
        rules.keywordPatterns = config.keywordDetection || {};

        return rules;
    }

    detectChannel(input) {
        try {
            if (!input || typeof input !== 'string') {
                return { channel: null, confidence: 0, method: 'invalid_input' };
            }

            // 1) 特定渠道快速路径
            const fliggyResult = this.detectFliggy(input);
            if (fliggyResult.confidence > 0.8) return fliggyResult;

            const jinggeResult = this.detectJingGe(input);
            if (jinggeResult.confidence > 0.8) return jinggeResult;

            // 2) 通用参考号/关键词
            const referenceResult = this.detectByReference(input);
            if (referenceResult.confidence > 0.8) return referenceResult;

            const keywordResult = this.detectByKeywords(input);
            if (keywordResult.confidence > 0.6) return keywordResult;

            // 3) 通用通道遍历匹配（最小化新增）
            let best = { channel: null, confidence: 0, method: 'generic_no_match' };
            for (const [key, rule] of Object.entries(this.detectionRules)) {
                if (!rule || key === 'referencePatterns' || key === 'keywordPatterns') continue;
                const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
                for (const pattern of patterns) {
                    try {
                        if (pattern && pattern.test && pattern.test(input)) {
                            const conf = typeof rule.confidence === 'number' ? rule.confidence : 0.8;
                            if (conf > best.confidence) {
                                best = {
                                    channel: rule.channel || key,
                                    confidence: conf,
                                    method: 'generic_pattern',
                                    matchedPattern: pattern.source || String(pattern)
                                };
                            }
                        }
                    } catch {}
                }
            }
            if (best.confidence > 0) return best;

            return { channel: null, confidence: 0, method: 'no_match' };

        } catch (error) {
            console.error('渠道检测失败:', error);
            return { channel: null, confidence: 0, method: 'error', error: error.message };
        }
    }

    detectFliggy(text) {
        const rules = this.detectionRules.fliggy;
        if (!rules) return { channel: null, confidence: 0 };
        let maxConfidence = 0;
        let matchedPattern = null;

        for (const pattern of rules.patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                if (pattern.source.includes('订单编号')) {
                    maxConfidence = 0.95;
                    matchedPattern = '订单编号+19位数字';
                    break;
                } else {
                    maxConfidence = Math.max(maxConfidence, 0.85);
                    matchedPattern = pattern.source;
                }
            }
        }

        return {
            channel: maxConfidence > 0 ? rules.channel : null,
            confidence: maxConfidence,
            method: 'fliggy_pattern',
            matchedPattern
        };
    }

    detectJingGe(text) {
        const rules = this.detectionRules.jingge;
        if (!rules) return { channel: null, confidence: 0 };
        let maxConfidence = 0;
        let matchedPattern = null;

        for (const pattern of rules.patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                maxConfidence = rules.confidence;
                matchedPattern = pattern.source;
                break;
            }
        }

        return {
            channel: maxConfidence > 0 ? rules.channel : null,
            confidence: maxConfidence,
            method: 'jingge_pattern',
            matchedPattern
        };
    }

    detectByReference(text) {
        const referencePattern = /\b([A-Z]{2})[A-Z0-9]{6,12}\b/g;
        const matches = text.match(referencePattern);

        if (matches && matches.length > 0) {
            for (const match of matches) {
                const prefix = match.substring(0, 2);
                const rule = this.detectionRules.referencePatterns[prefix];
                
                if (rule) {
                    return {
                        channel: rule.channel,
                        confidence: rule.confidence,
                        method: 'reference_pattern',
                        matchedReference: match
                    };
                }
            }
        }

        return { channel: null, confidence: 0, method: 'reference_no_match' };
    }

    detectByKeywords(text) {
        const lowerText = text.toLowerCase();

        for (const [keyword, rule] of Object.entries(this.detectionRules.keywordPatterns || {})) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return {
                    channel: rule.channel,
                    confidence: rule.confidence,
                    method: 'keyword_match',
                    matchedKeyword: keyword
                };
            }
        }

        return { channel: null, confidence: 0, method: 'keyword_no_match' };
    }

    getDetectionRules() {
        const clone = {};
        for (const [key, rule] of Object.entries(this.detectionRules)) {
            if (key === 'referencePatterns' || key === 'keywordPatterns') {
                clone[key] = rule;
                continue;
            }
            if (!rule || !Array.isArray(rule.patterns)) continue;
            clone[key] = {
                channel: rule.channel,
                confidence: rule.confidence,
                patterns: rule.patterns.map(p => p instanceof RegExp ? p.source : (typeof p === 'string' ? p : ''))
            };
        }
        return clone;
    }

    updateDetectionRules(newRules) {
        for (const [key, rule] of Object.entries(newRules)) {
            if (key === 'referencePatterns' || key === 'keywordPatterns') {
                this.detectionRules[key] = rule;
                continue;
            }
            if (!rule) continue;
            const patterns = Array.isArray(rule.patterns) ? rule.patterns : [];
            this.detectionRules[key] = {
                channel: rule.channel || this.detectionRules[key]?.channel || key,
                confidence: typeof rule.confidence === 'number' ? rule.confidence : (this.detectionRules[key]?.confidence || 0.8),
                patterns: patterns
                    .map(p => {
                        try {
                            if (p instanceof RegExp) return p;
                            if (typeof p === 'string') return this.dataUtils.patternToRegex(p);
                            return null;
                        } catch { return null; }
                    })
                    .filter(Boolean)
            };
        }
        console.log('检测规则已更新', this.detectionRules);
        return true;
    }

    addChannelRule(channelName, patterns, confidence = 0.8) {
        this.detectionRules[channelName.toLowerCase()] = {
            patterns: patterns.map(p => new RegExp(p)),
            confidence: confidence,
            channel: channelName
        };
        console.log(`已添加渠道规则: ${channelName}`);
        return true;
    }
}

// 模块工厂函数
function createChannelDetectorModule(container) {
    const configManager = container.get('config');
    return new ChannelDetector(configManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('channelDetector', createChannelDetectorModule, ['config']);
    console.log('📦 ChannelDetector已注册到模块容器');
}