/**
 * prompt-fragments.js - 智能提示词片段管理器（字段模块化版）
 *
 * 设计哲学: "字段模块化" - 基于字段的独立片段组合
 * - 字段级别的提示词片段管理
 * - 渠道独立存储 + generic回退机制
 * - 支持AI优化的字段模块化架构
 *
 * 核心功能:
 * - 通用字段片段模板管理 (getUniversalFieldTemplates)
 * - 字段列表管理 (getCoreFieldList)
 * - 渠道特定片段支持 (向后兼容)
 * - 与提示词片段编辑器集成
 *
 * 注意：提示词组合功能已迁移到 prompt-composer.js
 */

class PromptFragmentManager {
    constructor() {
        this.basePrompt = this.createBasePrompt();
        this.channelFragments = this.createChannelFragments();
        this.fieldFragments = this.createFieldFragments();
    }

    /**
     * 创建通用基础提示词 - 覆盖绝大多数场景
     */
    createBasePrompt() {
        return {
            role: `You are an expert booking data extraction system. Extract ALL information with maximum accuracy.`,
            
            format: `Output EXACTLY one JSON object. No explanations, no additional text.`,
            
            fields: {
                // 核心字段定义 - 每个字段都有详细的识别规则
                ota: `Platform/Channel name (KKday, Klook, Ctrip, etc.) or null`,
                ota_reference_number: `Booking ID: order number, reference code, booking confirmation (6+ chars)`,
                customer_name: `Full name: subscriber/passenger/contact person (first+last name)`,
                customer_contact: `Phone number: include country code (+852-12345678, +60-*********)`,
                customer_email: `Email address: valid format (<EMAIL>)`,
                date: `Service date: YYYY-MM-DD format (travel/pickup/service date)`,
                time: `Service time: HH:MM 24-hour format (pickup/departure time)`,
                pickup: `Pickup location: full address/hotel name/landmark`,
                destination: `Destination: full address/airport/landmark/same as pickup for tours`,
                flight_info: `Flight details: flight number + time/date if mentioned`,
                passenger_number: `Total passengers: adults + children (numeric only)`,
                luggage_number: `Luggage count: pieces/bags/suitcases (numeric only)`,
                ota_price: `Total cost: numeric value only, remove currency symbols`,
                sub_category_id: `Service type: 2=airport pickup, 3=airport dropoff, 4=charter/tour/sightseeing`,
                car_type_id: `Vehicle type ID: if specific car model mentioned`,
                languages_id_array: `Language services: {"0": language_id} if language requirements specified`,
                extra_requirement: `Special requests: max 120 chars (routes, vehicle specs, language, accessibility)`
            },

            extraction_rules: [
                `Names: Extract from "订购人", "客户", "姓名", "passenger", "contact person"`,
                `Phones: Keep international format, extract from "电话", "手机", "联系", "phone", "contact"`,
                `Dates: Convert any format to YYYY-MM-DD, look for "日期", "使用日期", "接驳日期", "date"`,
                `Times: Convert to 24-hour HH:MM, extract from "时间", "接驳时间", "出发", "pickup time"`,
                `Locations: Extract from "地点", "上车", "下车", "pickup", "destination", hotel names`,
                `Prices: Extract numeric only from "金额", "费用", "价格", "cost", remove MYR/USD/CNY`,
                `Routes: For tours/charters, summarize route in extra_requirement`,
                `Vehicle: Note car type in extra_requirement if mentioned`,
                `Languages: Note language requirements in extra_requirement`
            ]
        };
    }

    /**
     * 创建渠道特定片段 - 针对不同OTA平台的特殊格式
     */
    createChannelFragments() {
        return {
            kkday: {
                name: "KKday专用片段",
                patterns: [
                    `Look for "订单编号" pattern: usually starts with numbers+KK+numbers`,
                    `Customer: "订购人" or "联络人姓名"`,
                    `Price: "成本金额MYR" - extract numeric value`,
                    `Dates: "使用日期" for service date, "接駁日期" for pickup date`,
                    `Time: "接駁時間" for pickup time`,
                    `Locations: "上车地点", "下车地点"`,
                    `Tours: Look for "包车路线" and summarize in extra_requirement`,
                    `Vehicle: "车辆类型" - note in extra_requirement`,
                    `Language: "指南选项" - note driver language in extra_requirement`,
                    `Passengers: "成人人数", "孩童人数" - sum for total`
                ],
                context: `This is a KKday booking with structured Chinese format.`
            },

            klook: {
                name: "Klook专用片段", 
                patterns: [
                    `Booking ID: Usually alphanumeric, may start with letters`,
                    `Customer info in structured format`,
                    `Prices often in local currency`,
                    `Activity/tour bookings common`
                ],
                context: `This is a Klook experience booking.`
            },

            ctrip: {
                name: "携程专用片段",
                patterns: [
                    `Chinese language booking format`,
                    `Look for "携程订单", "订单号"`,
                    `Prices in CNY currency`,
                    `Flight info commonly included`
                ],
                context: `This is a Ctrip (携程) booking in Chinese.`
            },

            fliggy: {
                name: "飞猪专用片段",
                patterns: [
                    `19-digit order numbers common`,
                    `Alibaba/Taobao style format`,
                    `Chinese language`,
                    `"订单编号：19位数字" pattern`
                ],
                context: `This is a Fliggy (飞猪) Alibaba travel booking.`
            }
        };
    }

    /**
     * 创建字段特定增强片段 - 针对复杂字段的专门处理
     */
    createFieldFragments() {
        return {
            extra_requirement: {
                tour_routes: `For tours: Format as "Route: location1-location2-location3"`,
                vehicle_specs: `Vehicle type: Note specific car models, seating capacity`,
                language_service: `Language: Note driver/guide language requirements`,
                accessibility: `Special needs: wheelchair, child seats, elderly assistance`,
                timing: `Timing: early pickup, flexible schedule, waiting time`
            },

            locations: {
                airports: `Airport codes: KLIA, KLIA2, LCCT, Changi, etc.`,
                hotels: `Hotel names: Full name + area/district`,
                landmarks: `Tourist spots: Petronas Towers, Marina Bay, etc.`,
                addresses: `Full addresses: Include postal codes if present`
            },

            pricing: {
                currencies: `Handle MYR, SGD, USD, CNY, THB currencies`,
                formats: `Price patterns: "MYR 520.00", "RM520", "$520", "520元"`,
                totals: `Extract final total, ignore item prices`
            }
        };
    }

    

    /**
     * 获取核心字段列表 - 基于 basePrompt.fields 的字段全集
     * @returns {Array<string>} 字段名数组
     */
    getCoreFieldList() {
        return Object.keys(this.basePrompt.fields);
    }

    /**
     * 获取通用字段片段提示词模板 - 用于提示词片段编辑器的模板应用
     * @returns {Object} 字段名 -> 通用模板字符串的映射
     */
    getUniversalFieldTemplates() {
        return {
            // 平台与订单信息
            ota: `Extract platform name: KKday, Klook, Ctrip, 携程, 飞猪, Fliggy. Return exact name or null.`,
            ota_reference_number: `Find booking reference/order number (6+ chars). Labels: "订单编号", "Booking ID", "Reference".`,

            // 客户信息
            customer_name: `Extract full customer name. Labels: "订购人", "客户姓名", "Passenger", "Contact Person".`,
            customer_contact: `Extract phone with country code (+852-12345678). Labels: "电话", "手机", "Phone".`,
            customer_email: `Extract email address (<EMAIL>). Labels: "邮箱", "Email".`,

            // 时间信息
            date: `Extract service date in YYYY-MM-DD format. Labels: "使用日期", "服务日期", "Travel Date".`,
            time: `Extract time in HH:MM 24-hour format. Labels: "接驳时间", "出发时间", "Pickup Time".`,

            // 地点信息
            pickup: `Extract pickup location with full address. Labels: "上车地点", "接客地点", "Pickup Location".`,
            destination: `Extract destination address. Labels: "下车地点", "目的地", "Destination".`,

            // 航班与乘客信息
            flight_info: `Extract flight number and time. Labels: "航班信息", "Flight". Format: CA1234.`,
            passenger_number: `Extract total passenger count (adults + children). Return numeric value only.`,
            luggage_number: `Extract luggage count. Labels: "行李数量", "Luggage". Return numeric value only.`,

            // 价格与服务类型
            ota_price: `Extract total cost as number only. Remove currency symbols (MYR, SGD, USD, CNY, RM, $, ¥).`,
            sub_category_id: `Service type: 2=airport pickup, 3=airport dropoff, 4=charter/tour.`,
            car_type_id: `Extract vehicle type ID if mentioned. Look for car model/seating capacity.`,

            // 语言与特殊要求
            languages_id_array: `Extract language requirements. Format: {"0": language_id}.`,
            extra_requirement: `Extract special requests, max 120 chars. Include: routes, vehicle specs, accessibility.`
        };
    }

    /**
     * 获取提示词片段
     * @param {string} field - 字段名
     * @param {string} channelId - 渠道ID (可选)
     * @returns {string|null} 提示词片段内容
     */
    getPromptFragment(field, channelId = null) {
        // 1. 尝试获取渠道特定字段片段
        if (channelId && this.channelFragments[channelId] && this.channelFragments[channelId].patterns) {
            const channelSpecificPattern = this.channelFragments[channelId].patterns.find(p => p.includes(field));
            if (channelSpecificPattern) {
                return channelSpecificPattern; // Simplified: just return the pattern string
            }
        }

        // 2. 尝试获取字段特定增强片段
        if (this.fieldFragments[field]) {
            // For simplicity, return the first value of the field fragment object
            const fragmentKeys = Object.keys(this.fieldFragments[field]);
            if (fragmentKeys.length > 0) {
                return this.fieldFragments[field][fragmentKeys[0]];
            }
        }

        // 3. 尝试获取基础提示词字段
        if (this.basePrompt.fields[field]) {
            return this.basePrompt.fields[field];
        }

        // 4. 回退到通用模板（如果存在）
        const universalTemplate = this.getUniversalFieldTemplates()[field];
        if (universalTemplate) {
            return universalTemplate;
        }

        return null;
    }

    /**
     * 获取所有字段名称
     * @returns {Array<string>} 所有字段名称的数组
     */
    getAllFieldNames() {
        return Object.keys(this.basePrompt.fields);
    }

    /**
     * 构建完整的提示词
     * @param {string} channelId - 渠道ID
     * @param {Array<string>} fields - 需要的字段列表
     * @returns {string} 完整的提示词
     */
    buildPrompt(channelId = null, fields = []) {
        let prompt = "";
        
        // 确保 fields 是数组
        if (!Array.isArray(fields)) {
            fields = [];
        }
        
        // 添加基础上下文
        prompt += this.basePrompt.context + "\n\n";
        
        // 添加渠道特定上下文（如果有）
        if (channelId && this.channelFragments[channelId]) {
            prompt += this.channelFragments[channelId].context + "\n\n";
        }
        
        // 添加字段说明
        prompt += "请提取以下字段：\n";
        
        // 如果没有指定字段，使用所有基础字段
        if (fields.length === 0) {
            fields = this.getAllFieldNames();
        }
        
        // 为每个字段添加说明
        fields.forEach(field => {
            const fragment = this.getPromptFragment(field, channelId);
            if (fragment) {
                prompt += `- ${field}: ${fragment}\n`;
            }
        });
        
        // 添加提取规则
        if (this.basePrompt.extraction_rules) {
            prompt += "\n提取规则：\n";
            this.basePrompt.extraction_rules.forEach(rule => {
                prompt += `- ${rule}\n`;
            });
        }
        
        // 添加渠道特定模式（如果有）
        if (channelId && this.channelFragments[channelId] && this.channelFragments[channelId].patterns) {
            prompt += "\n渠道特定识别模式：\n";
            this.channelFragments[channelId].patterns.forEach(pattern => {
                prompt += `- ${pattern}\n`;
            });
        }
        
        return prompt.trim();
    }
}

// 模块工厂函数
function createPromptFragmentManagerModule(container) {
    return new PromptFragmentManager();
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('promptFragmentManager', createPromptFragmentManagerModule, []);
    console.log('📦 PromptFragmentManager已注册到模块容器');
}
