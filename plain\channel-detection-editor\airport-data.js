'use strict';

// ==================== 机场数据配置 ====================

/**
 * 主要机场中英文名称映射表
 * @DATA_STRUCTURE 机场信息完整映射
 */
const AIRPORT_MAPPINGS = {
    // 马来西亚机场
    'kuala_lumpur': {
        chinese_names: ['吉隆坡机场', '吉隆坡国际机场', 'KLIA', '雪邦机场'],
        english_name: 'Kuala Lumpur International Airport',
        iata_code: 'KUL',
        icao_code: 'WMKK',
        city: 'Kuala Lumpur',
        country: 'Malaysia',
        region: '吉隆坡'
    },
    'kuala_lumpur_2': {
        chinese_names: ['吉隆坡第二国际机场', 'KLIA2', '廉价航空机场'],
        english_name: 'Kuala Lumpur International Airport 2',
        iata_code: 'KUL',
        icao_code: 'WMKK',
        city: 'Kuala Lumpur',
        country: 'Malaysia',
        region: '吉隆坡'
    },
    'penang': {
        chinese_names: ['槟城机场', '槟城国际机场', '槟岛机场'],
        english_name: 'Penang International Airport',
        iata_code: 'PEN',
        icao_code: 'WMKP',
        city: 'Penang',
        country: 'Malaysia',
        region: '槟城'
    },
    'johor_bahru': {
        chinese_names: ['新山机场', '柔佛机场', '士乃机场'],
        english_name: 'Johor Bahru Senai International Airport',
        iata_code: 'JHB',
        icao_code: 'WMKJ',
        city: 'Johor Bahru',
        country: 'Malaysia',
        region: '新山'
    },
    'kota_kinabalu': {
        chinese_names: ['亚庇机场', '哥打京那巴鲁机场', '沙巴机场'],
        english_name: 'Kota Kinabalu International Airport',
        iata_code: 'BKI',
        icao_code: 'WBKK',
        city: 'Kota Kinabalu',
        country: 'Malaysia',
        region: '亚庇'
    },
    'kuching': {
        chinese_names: ['古晋机场', '砂拉越机场'],
        english_name: 'Kuching International Airport',
        iata_code: 'KCH',
        icao_code: 'WBGG',
        city: 'Kuching',
        country: 'Malaysia',
        region: '古晋'
    },

    // 新加坡机场
    'singapore': {
        chinese_names: ['新加坡机场', '樟宜机场', '新加坡樟宜机场', '新加坡国际机场'],
        english_name: 'Singapore Changi Airport',
        iata_code: 'SIN',
        icao_code: 'WSSS',
        city: 'Singapore',
        country: 'Singapore',
        region: '新加坡'
    },

    // 泰国机场
    'bangkok_suvarnabhumi': {
        chinese_names: ['曼谷机场', '素万那普机场', '曼谷国际机场'],
        english_name: 'Suvarnabhumi International Airport',
        iata_code: 'BKK',
        icao_code: 'VTBS',
        city: 'Bangkok',
        country: 'Thailand',
        region: '曼谷'
    },
    'bangkok_don_mueang': {
        chinese_names: ['廊曼机场', '曼谷廊曼机场', '旧曼谷机场'],
        english_name: 'Don Mueang International Airport',
        iata_code: 'DMK',
        icao_code: 'VTBD',
        city: 'Bangkok',
        country: 'Thailand',
        region: '曼谷'
    },
    'phuket': {
        chinese_names: ['普吉机场', '普吉岛机场', '普吉国际机场'],
        english_name: 'Phuket International Airport',
        iata_code: 'HKT',
        icao_code: 'VTSP',
        city: 'Phuket',
        country: 'Thailand',
        region: '普吉'
    },

    // 印尼机场
    'jakarta': {
        chinese_names: ['雅加达机场', '苏加诺-哈达机场', '雅加达国际机场'],
        english_name: 'Soekarno-Hatta International Airport',
        iata_code: 'CGK',
        icao_code: 'WIII',
        city: 'Jakarta',
        country: 'Indonesia',
        region: '雅加达'
    },
    'bali': {
        chinese_names: ['巴厘岛机场', '登巴萨机场', '努拉莱伊机场'],
        english_name: 'Ngurah Rai International Airport',
        iata_code: 'DPS',
        icao_code: 'WADD',
        city: 'Denpasar',
        country: 'Indonesia',
        region: '巴厘岛'
    },

    // 越南机场
    'ho_chi_minh': {
        chinese_names: ['胡志明市机场', '新山一机场', '西贡机场'],
        english_name: 'Tan Son Nhat International Airport',
        iata_code: 'SGN',
        icao_code: 'VVTS',
        city: 'Ho Chi Minh City',
        country: 'Vietnam',
        region: '胡志明市'
    },
    'hanoi': {
        chinese_names: ['河内机场', '内排机场', '河内国际机场'],
        english_name: 'Noi Bai International Airport',
        iata_code: 'HAN',
        icao_code: 'VVNB',
        city: 'Hanoi',
        country: 'Vietnam',
        region: '河内'
    },

    // 菲律宾机场
    'manila': {
        chinese_names: ['马尼拉机场', '尼诺·阿基诺机场', '马尼拉国际机场'],
        english_name: 'Ninoy Aquino International Airport',
        iata_code: 'MNL',
        icao_code: 'RPLL',
        city: 'Manila',
        country: 'Philippines',
        region: '马尼拉'
    },

    // 中国主要机场
    'guangzhou': {
        chinese_names: ['广州机场', '白云机场', '广州白云机场'],
        english_name: 'Guangzhou Baiyun International Airport',
        iata_code: 'CAN',
        icao_code: 'ZGGG',
        city: 'Guangzhou',
        country: 'China',
        region: '广州'
    },
    'shenzhen': {
        chinese_names: ['深圳机场', '宝安机场', '深圳宝安机场'],
        english_name: 'Shenzhen Bao\'an International Airport',
        iata_code: 'SZX',
        icao_code: 'ZGSZ',
        city: 'Shenzhen',
        country: 'China',
        region: '深圳'
    }
};

/**
 * 机场关键词识别规则
 * @ENUMERATION 机场识别关键词分类
 */
const AIRPORT_KEYWORDS = {
    // 中文关键词
    chinese: [
        '机场', '国际机场', '航站楼', '候机楼', '机场大厅',
        '出发厅', '到达厅', '登机口', '航班', '起飞', '降落'
    ],
    // 英文关键词
    english: [
        'airport', 'international airport', 'terminal', 'departure',
        'arrival', 'gate', 'flight', 'takeoff', 'landing'
    ],
    // 机场代码模式
    codes: [
        /\b[A-Z]{3}\b/g,  // IATA代码 (3字母)
        /\b[A-Z]{4}\b/g   // ICAO代码 (4字母)
    ]
};

/**
 * 城市到机场的映射
 * @REFERENCE 城市名称到机场标识符的快速查找
 */
const CITY_TO_AIRPORT = {
    // 中文城市名
    '吉隆坡': 'kuala_lumpur',
    '槟城': 'penang',
    '新山': 'johor_bahru',
    '亚庇': 'kota_kinabalu',
    '古晋': 'kuching',
    '新加坡': 'singapore',
    '曼谷': 'bangkok_suvarnabhumi',
    '普吉': 'phuket',
    '雅加达': 'jakarta',
    '巴厘岛': 'bali',
    '胡志明市': 'ho_chi_minh',
    '河内': 'hanoi',
    '马尼拉': 'manila',
    '广州': 'guangzhou',
    '深圳': 'shenzhen',
    
    // 英文城市名
    'kuala lumpur': 'kuala_lumpur',
    'penang': 'penang',
    'johor bahru': 'johor_bahru',
    'kota kinabalu': 'kota_kinabalu',
    'kuching': 'kuching',
    'singapore': 'singapore',
    'bangkok': 'bangkok_suvarnabhumi',
    'phuket': 'phuket',
    'jakarta': 'jakarta',
    'bali': 'bali',
    'ho chi minh': 'ho_chi_minh',
    'hanoi': 'hanoi',
    'manila': 'manila',
    'guangzhou': 'guangzhou',
    'shenzhen': 'shenzhen'
};

// ==================== 导出数据对象 ====================

const AIRPORT_DATA_SOURCE = {
    mappings: AIRPORT_MAPPINGS,
    keywords: AIRPORT_KEYWORDS,
    cityToAirport: CITY_TO_AIRPORT,
    
    /**
     * 获取所有机场信息
     * @UTIL 获取完整机场数据列表
     */
    getAllAirports() {
        return Object.values(this.mappings);
    },
    
    /**
     * 根据机场标识符获取机场信息
     * @UTIL 机场信息查询工具
     */
    getAirportById(airportId) {
        return this.mappings[airportId] || null;
    },
    
    /**
     * 根据城市名获取机场信息
     * @UTIL 城市到机场的映射查询
     */
    getAirportByCity(cityName) {
        const airportId = this.cityToAirport[cityName.toLowerCase()];
        return airportId ? this.mappings[airportId] : null;
    },
    
    /**
     * 检查文本是否包含机场关键词
     * @UTIL 机场内容识别工具
     */
    containsAirportKeywords(text) {
        const lowerText = text.toLowerCase();
        
        // 检查中文关键词
        for (const keyword of this.keywords.chinese) {
            if (text.includes(keyword)) return true;
        }
        
        // 检查英文关键词
        for (const keyword of this.keywords.english) {
            if (lowerText.includes(keyword)) return true;
        }
        
        // 检查机场代码
        for (const pattern of this.keywords.codes) {
            // Resetting lastIndex for global regexes
            pattern.lastIndex = 0;
            if (pattern.test(text)) return true;
        }
        
        return false;
    }
};

// 暴露到全局变量
window.AIRPORT_DATA_SOURCE = AIRPORT_DATA_SOURCE;
