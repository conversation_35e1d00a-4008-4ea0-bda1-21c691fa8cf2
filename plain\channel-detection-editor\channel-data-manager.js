/**
 * 统一渠道数据管理服务
 * 负责管理渠道列表，确保提示词编辑器和渠道规则系统的数据同步
 */
class ChannelDataManager {
    constructor(localStorageManager) {
        this.localStorageManager = localStorageManager;
        this.channels = new Map();
        this.listeners = new Set();
        this.initialize();
    }

    async initialize() {
        await this.loadChannels();
        this.setupEventListeners();
        console.log('ChannelDataManager initialized');
    }

    async loadChannels() {
        try {
            // 从localStorage加载渠道规则
            const channelRules = await this.localStorageManager.loadData('channelDetectionRules') || {};
            // 从localStorage加载提示词片段
            const promptSnippets = await this.localStorageManager.loadData('promptSnippets') || {};

            // 合并渠道列表
            const ruleChannels = Object.keys(channelRules).filter(key =>
                !['referencePatterns', 'keywordPatterns'].includes(key)
            );
            const promptChannels = Object.keys(promptSnippets);

            const allChannels = new Set([...ruleChannels, ...promptChannels]);

            this.channels.clear();
            for (const channel of allChannels) {
                this.channels.set(channel, {
                    name: channel,
                    hasRules: ruleChannels.includes(channel),
                    hasPrompts: promptChannels.includes(channel),
                    lastUpdated: new Date().toISOString()
                });
            }

            console.log('Loaded channels:', Array.from(this.channels.keys()));
        } catch (error) {
            console.error('Failed to load channels:', error);
        }
    }

    setupEventListeners() {
        // 监听渠道规则更新事件
        window.addEventListener('channelsUpdated', (event) => {
            this.handleChannelsUpdated(event.detail);
        });

        // 监听提示词更新事件
        window.addEventListener('promptsUpdated', (event) => {
            this.handlePromptsUpdated(event.detail);
        });
    }

    handleChannelsUpdated(detail) {
        const { channels, source } = detail;
        console.log('Channels updated from', source, ':', channels);

        // 更新渠道列表
        for (const channel of channels) {
            if (!this.channels.has(channel)) {
                this.channels.set(channel, {
                    name: channel,
                    hasRules: true,
                    hasPrompts: false,
                    lastUpdated: new Date().toISOString()
                });
            } else {
                const existing = this.channels.get(channel);
                existing.hasRules = true;
                existing.lastUpdated = new Date().toISOString();
            }
        }

        this.notifyListeners('channelsUpdated', { channels: Array.from(this.channels.keys()) });
    }

    handlePromptsUpdated(detail) {
        const { channels, source } = detail;
        console.log('Prompts updated from', source, ':', channels);

        // 更新渠道列表
        for (const channel of channels) {
            if (!this.channels.has(channel)) {
                this.channels.set(channel, {
                    name: channel,
                    hasRules: false,
                    hasPrompts: true,
                    lastUpdated: new Date().toISOString()
                });
            } else {
                const existing = this.channels.get(channel);
                existing.hasPrompts = true;
                existing.lastUpdated = new Date().toISOString();
            }
        }

        this.notifyListeners('promptsUpdated', { channels: Array.from(this.channels.keys()) });
    }

    getAllChannels() {
        return Array.from(this.channels.keys());
    }

    getChannelInfo(channelName) {
        return this.channels.get(channelName) || null;
    }

    addListener(callback) {
        this.listeners.add(callback);
    }

    removeListener(callback) {
        this.listeners.delete(callback);
    }

    notifyListeners(eventType, data) {
        for (const listener of this.listeners) {
            try {
                listener(eventType, data);
            } catch (error) {
                console.error('Error notifying listener:', error);
            }
        }
    }

    // 发布渠道更新事件
    publishChannelsUpdate(channels, source) {
        const event = new CustomEvent('channelsUpdated', {
            detail: { channels, source }
        });
        window.dispatchEvent(event);
    }

    // 发布提示词更新事件
    publishPromptsUpdate(channels, source) {
        const event = new CustomEvent('promptsUpdated', {
            detail: { channels, source }
        });
        window.dispatchEvent(event);
    }
}

// 工厂函数
window.createChannelDataManagerModule = function(container) {
    const localStorageManager = container.get('localStorageManager');
    return new ChannelDataManager(localStorageManager);
};
