/**
 * 统一配置管理模块 (DEBUGGING - MINIMAL VERSION)
 */
class ConfigManager {
    constructor(otaSource, airportDataSource) {
        this.otaSource = otaSource; // 全局依赖：OTA数据源 @DEPENDENCY
        this.airportDataSource = airportDataSource; // 全局依赖：机场数据源 @DEPENDENCY
        this.config = this.loadDefaultConfig(); // 初始化最小配置 @INIT
        // 暴露工具集合：统一正则构建工具，供外部模块使用（纯文本→不区分大小写的正则） @UTIL
        this.DataUtils = {
            patternToRegex: (text) => this._patternToRegex(String(text || '')) // 防御性：空值转空串 @LIFECYCLE
        };
        console.log('✅ MINIMAL ConfigManager Initialized for Debugging'); // 初始化日志 @LIFECYCLE
    }

    loadDefaultConfig() {
        // RETURNING MINIMAL CONFIG FOR DEBUGGING @CONFIG_FILE
        return {
            COMPLETE_USER_LIST: [{ id: 1, name: 'Super Admin', role_id: 1 }], // 用户清单 @DATA_STRUCTURE
            COMPLETE_CHANNEL_LIST: ['Kkday', 'Ctrip API'], // 渠道清单 @DATA_STRUCTURE
            CHANNEL_DETECTION_RULES: {
                 klook: { name: 'Klook', displayName: 'Klook', patterns: ['klook', '客路'], confidence: 0.85 } // 最小规则示例 @DATA_STRUCTURE
            },
            VEHICLE_TYPES: [{ id: 5, name: '5 Seater', displayName: '5座轿车' }], // 车型配置 @DATA_STRUCTURE
            USER_PERMISSION_CONFIG: {
                defaultPermissions: { features: { canUsePaging: true } } // 权限配置 @DATA_STRUCTURE
            },
            REFERENCE_PATTERNS: {}, // 真实数据源接入位：参考号规则 @DECLARATION
            KEYWORD_DETECTION: {} // 真实数据源接入位：关键词规则 @DECLARATION
        };
    }

    // --- Getters for minimal data ---
    getAllUsers() { return this.config.COMPLETE_USER_LIST; }
    getAllChannels() { return this.config.COMPLETE_CHANNEL_LIST; }
    getUserPermissions() { return this.config.USER_PERMISSION_CONFIG.defaultPermissions; }
    // 优先返回“渠道规则编辑器”持久化内容；回退到默认配置 @LIFECYCLE
    getChannelDetectionRules() {
        try {
            const saved = (window.localStorageManager && window.localStorageManager.data)
                ? window.localStorageManager.data.channelDetectionRules
                : null; // 从本地实例内存中读取，避免解密/异步问题 @DEPENDENCY
            return saved && typeof saved === 'object' ? saved : this.config.CHANNEL_DETECTION_RULES;
        } catch { return this.config.CHANNEL_DETECTION_RULES; }
    }
    getVehicleTypes() { return this.config.VEHICLE_TYPES; }
    getOtaData() { return this.otaSource; }
    getHotelData() { return this.otaSource?.hotels || []; } // 酒店数据源，返回数组 @REFERENCE
    getAirportData() { return this.airportDataSource; } // 航空数据源 @REFERENCE

    // 统一正则构建：将纯文本安全转义后生成不区分大小写的正则 @UTIL
    _patternToRegex(patternText) {
        // 注意：字符类中需转义 ']' 和 '\\'；保持与外部模块一致性，避免语法错误 @DECLARATION
        return new RegExp(String(patternText || '').replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
    }

    // 真实数据源：参考号模式映射（可接入后端或静态资源） @SERVICE
    getReferencePatterns() {
        return this.config.REFERENCE_PATTERNS || {}; // 最小实现：空对象 @LIFECYCLE
    }

    // 真实数据源：关键词检测映射 @SERVICE
    getKeywordDetection() {
        return this.config.KEYWORD_DETECTION || {}; // 最小实现：空对象 @LIFECYCLE
    }
}

// 工厂函数：供DI容器使用 @FACTORY
window.createConfigModule = function() {
    return new ConfigManager(window.OTA_SOURCE, window.AIRPORT_DATA_SOURCE);
};