/**
 * Gemini AI配置管理模块 - Refactored
 */
class GeminiConfig {
    constructor(localStorageManager) {
        this.localStorageManager = localStorageManager;
        this.config = { /* ... (default config) ... */ };
        this.initialize();
    }

    async initialize() {
        console.log('✅ Gemini配置已初始化 (Refactored)');
        await this.loadApiKeyFromStorage();
    }

    async loadApiKeyFromStorage() {
        if (this.localStorageManager) {
            const storedKey = await this.localStorageManager.loadData('gemini_api_key');
            if (storedKey && this.validateApiKeyFormat(storedKey)) {
                this.config.api.apiKey = storedKey;
                console.log('🔑 从本地存储加载了Gemini API密钥');
            }
        }
    }

    async setApiKey(apiKey) {
        if (this.validateApiKeyFormat(apiKey)) {
            this.config.api.apiKey = apiKey;
            if (this.localStorageManager) {
                await this.localStorageManager.saveData('gemini_api_key', apiKey);
            }
            console.log('✅ API密钥已设置并保存');
            return true;
        } else {
            console.error('❌ API密钥格式无效');
            return false;
        }
    }

    // ... (rest of the methods like callGeminiAPI, etc. remain the same)
    validateApiKeyFormat(key) { return key && key.length > 30 && key.startsWith('AIza'); }
    isApiKeyValid() { return this.validateApiKeyFormat(this.config.api.apiKey); }
    // ... (other methods) ...
     async callGeminiAPIWithRetry(prompt, options = {}) {
        const maxRetries = this.config.features.maxRetries;
        const retryDelay = this.config.features.retryDelay;
        
        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
            const result = await this.callGeminiAPI(prompt, options);
            
            if (result.success || attempt > maxRetries) {
                return result;
            }
            
            if (attempt <= maxRetries) {
                console.log(`⏳ 第${attempt}次重试，${retryDelay}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }
}

// 模块工厂函数
function createGeminiModule(container) {
    const localStorageManager = container.get('localStorageManager');
    return new GeminiConfig(localStorageManager);
}

// 注册到模块容器
if (typeof window !== 'undefined' && window.registerModule) {
    window.registerModule('gemini', createGeminiModule, ['localStorageManager']);
    console.log('📦 GeminiConfig已注册到模块容器');
}