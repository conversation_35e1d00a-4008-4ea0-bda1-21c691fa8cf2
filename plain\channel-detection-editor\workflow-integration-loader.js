/**
 * 工作流集成加载器 - 统一加载和管理所有工作流优化工具
 * 
 * === 集成功能 ===
 * - 自动检测和加载工作流优化模块
 * - 统一的工作流配置管理
 * - 模块间协调和通信
 * - 工作流状态监控
 * - 统一的用户界面入口
 * 
 * === 加载顺序 ===
 * 1. 开发工作流优化器
 * 2. 用户工作流增强器  
 * 3. 维护工作流管理器
 * 4. 工作流效率监控器
 * 5. 统一控制面板
 * 
 * @INTEGRATION 工作流集成管理
 * @COORDINATION 模块协调控制
 * @MONITORING 统一监控管理
 */

class WorkflowIntegrationLoader {
    constructor(options = {}) {
        this.options = {
            // 加载配置
            enableDevWorkflow: true,
            enableUserWorkflow: true,
            enableMaintenanceWorkflow: true,
            enableEfficiencyMonitoring: true,
            
            // UI配置
            showUnifiedPanel: true,
            showIndividualPanels: true,
            panelPosition: 'right', // right, left, top, bottom
            
            // 集成配置
            enableCrossModuleComm: true,
            enableSharedState: true,
            enableUnifiedReporting: true,
            
            // 性能配置
            lazyLoading: true,
            loadingDelay: 1000,
            
            ...options
        };
        
        this.loadedModules = new Map();
        this.moduleStates = new Map();
        this.sharedState = new Map();
        this.communicationChannels = new Map();
        
        this.loadingStatus = {
            total: 0,
            loaded: 0,
            failed: 0,
            modules: []
        };
        
        this.initialize();
    }
    
    /**
     * 初始化工作流集成加载器
     */
    async initialize() {
        console.log('🚀 初始化工作流集成加载器...');
        
        // 设置加载界面
        this.setupLoadingUI();
        
        // 检测现有环境
        await this.detectEnvironment();
        
        // 加载工作流模块
        await this.loadWorkflowModules();
        
        // 设置统一控制面板
        if (this.options.showUnifiedPanel) {
            this.setupUnifiedPanel();
        }
        
        // 启动模块间通信
        if (this.options.enableCrossModuleComm) {
            this.setupCrossModuleCommunication();
        }
        
        // 启动效率监控
        if (this.options.enableEfficiencyMonitoring) {
            this.startEfficiencyMonitoring();
        }
        
        console.log('✅ 工作流集成加载完成');
        this.showWelcomeMessage();
    }
    
    /**
     * 设置加载界面
     */
    setupLoadingUI() {
        const loadingPanel = document.createElement('div');
        loadingPanel.id = 'workflow-loading-panel';
        loadingPanel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        `;
        
        loadingPanel.innerHTML = `
            <div style="
                padding: 24px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
            ">
                <div style="font-size: 32px; margin-bottom: 8px;">🚀</div>
                <h3 style="margin: 0; font-size: 20px;">工作流优化系统</h3>
                <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">正在加载优化工具...</p>
            </div>
            
            <div style="padding: 24px;">
                <div style="margin-bottom: 16px;">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 8px;
                    ">
                        <span style="font-size: 14px; color: #4a5568;" id="loading-status">初始化中...</span>
                        <span style="font-size: 12px; color: #6b7280;" id="loading-progress">0%</span>
                    </div>
                    <div style="
                        width: 100%;
                        height: 8px;
                        background: #e2e8f0;
                        border-radius: 4px;
                        overflow: hidden;
                    ">
                        <div id="loading-progress-bar" style="
                            height: 100%;
                            background: linear-gradient(90deg, #667eea, #764ba2);
                            width: 0%;
                            transition: width 0.5s ease;
                        "></div>
                    </div>
                </div>
                
                <div id="loading-modules" style="
                    max-height: 150px;
                    overflow-y: auto;
                    font-size: 12px;
                    color: #6b7280;
                ">
                    <!-- 模块加载状态 -->
                </div>
            </div>
        `;
        
        document.body.appendChild(loadingPanel);
        this.loadingPanel = loadingPanel;
    }
    
    /**
     * 检测现有环境
     */
    async detectEnvironment() {
        this.updateLoadingStatus('检测系统环境...', 10);
        
        const environment = {
            hasModuleContainer: !!window.moduleContainer,
            hasCacheManager: !!(window.cacheManager || (window.moduleContainer && window.moduleContainer.has('cacheManager'))),
            hasPerformanceBenchmark: !!document.querySelector('[src*="performance-benchmark-analyzer"]'),
            hasTestingSuite: !!document.querySelector('[src*="comprehensive-test-suite"]'),
            hasMainApplication: typeof window.processInput === 'function'
        };
        
        console.log('📋 环境检测结果:', environment);
        
        // 根据环境调整加载策略
        if (!environment.hasModuleContainer) {
            console.warn('⚠️ 未检测到模块容器，将使用兼容模式');
        }
        
        this.environment = environment;
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    /**
     * 加载工作流模块
     */
    async loadWorkflowModules() {
        const modulesToLoad = [
            {
                name: 'development-workflow',
                displayName: '开发工作流优化器',
                enabled: this.options.enableDevWorkflow,
                priority: 1,
                checker: () => !!window.workflowDevOptimizer,
                loader: () => this.loadDevWorkflowModule()
            },
            {
                name: 'user-workflow',
                displayName: '用户工作流增强器',
                enabled: this.options.enableUserWorkflow,
                priority: 2,
                checker: () => !!window.userWorkflowEnhancer,
                loader: () => this.loadUserWorkflowModule()
            },
            {
                name: 'maintenance-workflow',
                displayName: '维护工作流管理器',
                enabled: this.options.enableMaintenanceWorkflow,
                priority: 3,
                checker: () => !!window.maintenanceWorkflowManager,
                loader: () => this.loadMaintenanceWorkflowModule()
            }
        ];
        
        this.loadingStatus.total = modulesToLoad.filter(m => m.enabled).length;
        
        for (const module of modulesToLoad) {
            if (!module.enabled) continue;
            
            try {
                this.updateLoadingStatus(`加载 ${module.displayName}...`, 
                    20 + (this.loadingStatus.loaded / this.loadingStatus.total) * 60);
                
                // 检查模块是否已存在
                if (module.checker()) {
                    this.addModuleStatus(module.name, '已存在', 'success');
                    this.loadedModules.set(module.name, window[module.name.replace('-', '')]);
                } else {
                    // 加载模块
                    await module.loader();
                    
                    // 等待模块初始化
                    await this.waitForModule(module.checker, 5000);
                    
                    if (module.checker()) {
                        this.addModuleStatus(module.name, '加载成功', 'success');
                        this.loadedModules.set(module.name, window[module.name.replace('-', '')]);
                    } else {
                        throw new Error('模块初始化失败');
                    }
                }
                
                this.loadingStatus.loaded++;
                
            } catch (error) {
                console.error(`❌ 加载 ${module.displayName} 失败:`, error);
                this.addModuleStatus(module.name, '加载失败', 'error');
                this.loadingStatus.failed++;
            }
            
            await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        this.updateLoadingStatus('工作流模块加载完成', 80);
    }
    
    /**
     * 加载开发工作流模块
     */
    async loadDevWorkflowModule() {
        if (!window.workflowDevOptimizer) {
            // 如果模块未加载，等待其自动初始化
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        return true;
    }
    
    /**
     * 加载用户工作流模块
     */
    async loadUserWorkflowModule() {
        if (!window.userWorkflowEnhancer) {
            // 如果模块未加载，等待其自动初始化
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        return true;
    }
    
    /**
     * 加载维护工作流模块
     */
    async loadMaintenanceWorkflowModule() {
        if (!window.maintenanceWorkflowManager) {
            // 如果模块未加载，等待其自动初始化
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        return true;
    }
    
    /**
     * 等待模块加载完成
     */
    async waitForModule(checker, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            if (checker()) {
                return true;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        return false;
    }
    
    /**
     * 设置统一控制面板
     */
    setupUnifiedPanel() {
        this.updateLoadingStatus('设置统一控制面板...', 85);
        
        const unifiedPanel = document.createElement('div');
        unifiedPanel.id = 'unified-workflow-panel';
        unifiedPanel.style.cssText = `
            position: fixed;
            top: 20px;
            ${this.options.panelPosition === 'left' ? 'left: 20px' : 'right: 20px'};
            width: 300px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 9997;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        `;
        
        unifiedPanel.innerHTML = `
            <div style="
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
            ">
                <h3 style="margin: 0 0 8px 0; font-size: 18px;">🚀 工作流控制台</h3>
                <p style="margin: 0; font-size: 13px; opacity: 0.9;">统一的工作流管理中心</p>
            </div>
            
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #2d3748;">系统状态</h4>
                    <div id="workflow-system-status" style="
                        padding: 12px;
                        background: #f0fff4;
                        border-radius: 8px;
                        border-left: 4px solid #10b981;
                    ">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 6px;">
                            <span style="font-size: 13px; color: #2d3748;">整体状态</span>
                            <span style="font-size: 13px; color: #10b981; font-weight: 500;">运行正常</span>
                        </div>
                        <div style="font-size: 11px; color: #6b7280;">
                            已加载 ${this.loadingStatus.loaded} 个工作流模块
                        </div>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #2d3748;">快速访问</h4>
                    <div style="display: grid; gap: 8px;">
                        ${this.generateQuickAccessButtons()}
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #2d3748;">效率统计</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                        <div style="text-align: center; padding: 8px; background: #f7fafc; border-radius: 6px;">
                            <div style="font-size: 16px; font-weight: 600; color: #3182ce;" id="tasks-completed">0</div>
                            <div style="font-size: 10px; color: #6b7280;">任务完成</div>
                        </div>
                        <div style="text-align: center; padding: 8px; background: #f7fafc; border-radius: 6px;">
                            <div style="font-size: 16px; font-weight: 600; color: #10b981;" id="time-saved">0s</div>
                            <div style="font-size: 10px; color: #6b7280;">节省时间</div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 style="margin: 0 0 12px 0; font-size: 14px; color: #2d3748;">全局操作</h4>
                    <div style="display: flex; gap: 6px;">
                        <button id="unified-settings-btn" style="
                            flex: 1;
                            padding: 8px 12px;
                            border: 1px solid #667eea;
                            border-radius: 6px;
                            background: none;
                            color: #667eea;
                            cursor: pointer;
                            font-size: 12px;
                        ">设置</button>
                        <button id="unified-export-btn" style="
                            flex: 1;
                            padding: 8px 12px;
                            border: 1px solid #667eea;
                            border-radius: 6px;
                            background: none;
                            color: #667eea;
                            cursor: pointer;
                            font-size: 12px;
                        ">导出</button>
                        <button id="unified-help-btn" style="
                            flex: 1;
                            padding: 8px 12px;
                            border: 1px solid #667eea;
                            border-radius: 6px;
                            background: none;
                            color: #667eea;
                            cursor: pointer;
                            font-size: 12px;
                        ">帮助</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(unifiedPanel);
        this.unifiedPanel = unifiedPanel;
        
        this.bindUnifiedPanelEvents();
    }
    
    /**
     * 生成快速访问按钮
     */
    generateQuickAccessButtons() {
        const buttons = [];
        
        if (this.loadedModules.has('development-workflow')) {
            buttons.push(`
                <button class="quick-access-btn" data-action="open-dev-workflow" style="
                    width: 100%;
                    padding: 10px 12px;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    background: #f7fafc;
                    color: #4a5568;
                    cursor: pointer;
                    font-size: 12px;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: all 0.2s;
                ">
                    <span>🛠️</span>
                    <span>开发工作流</span>
                </button>
            `);
        }
        
        if (this.loadedModules.has('user-workflow')) {
            buttons.push(`
                <button class="quick-access-btn" data-action="open-user-workflow" style="
                    width: 100%;
                    padding: 10px 12px;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    background: #f7fafc;
                    color: #4a5568;
                    cursor: pointer;
                    font-size: 12px;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: all 0.2s;
                ">
                    <span>🎯</span>
                    <span>用户助手</span>
                </button>
            `);
        }
        
        if (this.loadedModules.has('maintenance-workflow')) {
            buttons.push(`
                <button class="quick-access-btn" data-action="open-maintenance-workflow" style="
                    width: 100%;
                    padding: 10px 12px;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    background: #f7fafc;
                    color: #4a5568;
                    cursor: pointer;
                    font-size: 12px;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: all 0.2s;
                ">
                    <span>🔧</span>
                    <span>系统维护</span>
                </button>
            `);
        }
        
        return buttons.join('');
    }
    
    /**
     * 绑定统一面板事件
     */
    bindUnifiedPanelEvents() {
        // 快速访问按钮
        const quickAccessButtons = this.unifiedPanel.querySelectorAll('.quick-access-btn');
        quickAccessButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.getAttribute('data-action');
                this.handleQuickAccess(action);
            });
            
            btn.addEventListener('mouseenter', () => {
                btn.style.backgroundColor = '#edf2f7';
                btn.style.borderColor = '#cbd5e0';
            });
            
            btn.addEventListener('mouseleave', () => {
                btn.style.backgroundColor = '#f7fafc';
                btn.style.borderColor = '#e2e8f0';
            });
        });
        
        // 全局操作按钮
        const settingsBtn = this.unifiedPanel.querySelector('#unified-settings-btn');
        const exportBtn = this.unifiedPanel.querySelector('#unified-export-btn');
        const helpBtn = this.unifiedPanel.querySelector('#unified-help-btn');
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showGlobalSettings());
        }
        
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportUnifiedReport());
        }
        
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.showHelpPanel());
        }
    }
    
    /**
     * 处理快速访问
     */
    handleQuickAccess(action) {
        switch (action) {
            case 'open-dev-workflow':
                if (window.workflowDevOptimizer) {
                    window.workflowDevOptimizer.showPanel();
                }
                break;
            case 'open-user-workflow':
                if (window.userWorkflowEnhancer) {
                    window.userWorkflowEnhancer.showPanel();
                }
                break;
            case 'open-maintenance-workflow':
                if (window.maintenanceWorkflowManager) {
                    window.maintenanceWorkflowManager.showMaintenancePanel();
                }
                break;
        }
    }
    
    /**
     * 设置跨模块通信
     */
    setupCrossModuleCommunication() {
        this.updateLoadingStatus('设置模块间通信...', 90);
        
        // 创建全局消息总线
        window.workflowMessageBus = {
            listeners: new Map(),
            
            subscribe: (event, callback) => {
                if (!window.workflowMessageBus.listeners.has(event)) {
                    window.workflowMessageBus.listeners.set(event, []);
                }
                window.workflowMessageBus.listeners.get(event).push(callback);
            },
            
            publish: (event, data) => {
                if (window.workflowMessageBus.listeners.has(event)) {
                    window.workflowMessageBus.listeners.get(event).forEach(callback => {
                        try {
                            callback(data);
                        } catch (error) {
                            console.error('消息处理失败:', error);
                        }
                    });
                }
            }
        };
        
        // 监听关键事件
        window.workflowMessageBus.subscribe('task-completed', (data) => {
            this.updateEfficiencyStats('tasks', 1);
        });
        
        window.workflowMessageBus.subscribe('time-saved', (data) => {
            this.updateEfficiencyStats('time', data.seconds || 0);
        });
    }
    
    /**
     * 启动效率监控
     */
    startEfficiencyMonitoring() {
        this.updateLoadingStatus('启动效率监控...', 95);
        
        this.efficiencyStats = {
            tasksCompleted: 0,
            timeSaved: 0,
            startTime: Date.now(),
            lastUpdate: Date.now()
        };
        
        // 定时更新统计
        setInterval(() => {
            this.updateEfficiencyDisplay();
        }, 5000);
    }
    
    /**
     * 更新效率统计
     */
    updateEfficiencyStats(type, value) {
        if (type === 'tasks') {
            this.efficiencyStats.tasksCompleted += value;
        } else if (type === 'time') {
            this.efficiencyStats.timeSaved += value;
        }
        
        this.efficiencyStats.lastUpdate = Date.now();
        this.updateEfficiencyDisplay();
    }
    
    /**
     * 更新效率显示
     */
    updateEfficiencyDisplay() {
        const tasksElement = document.getElementById('tasks-completed');
        const timeElement = document.getElementById('time-saved');
        
        if (tasksElement) {
            tasksElement.textContent = this.efficiencyStats.tasksCompleted;
        }
        
        if (timeElement) {
            const timeText = this.efficiencyStats.timeSaved > 60 ? 
                `${Math.floor(this.efficiencyStats.timeSaved / 60)}m` : 
                `${this.efficiencyStats.timeSaved}s`;
            timeElement.textContent = timeText;
        }
    }
    
    /**
     * 更新加载状态
     */
    updateLoadingStatus(status, progress) {
        const statusElement = document.getElementById('loading-status');
        const progressElement = document.getElementById('loading-progress');
        const progressBar = document.getElementById('loading-progress-bar');
        
        if (statusElement) statusElement.textContent = status;
        if (progressElement) progressElement.textContent = `${Math.round(progress)}%`;
        if (progressBar) progressBar.style.width = `${progress}%`;
    }
    
    /**
     * 添加模块状态
     */
    addModuleStatus(name, status, type) {
        const modulesContainer = document.getElementById('loading-modules');
        if (!modulesContainer) return;
        
        const statusColors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b'
        };
        
        const statusDiv = document.createElement('div');
        statusDiv.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid #f3f4f6;
        `;
        
        statusDiv.innerHTML = `
            <span>${name}</span>
            <span style="color: ${statusColors[type] || '#6b7280'}; font-weight: 500;">${status}</span>
        `;
        
        modulesContainer.appendChild(statusDiv);
    }
    
    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        this.updateLoadingStatus('初始化完成！', 100);
        
        setTimeout(() => {
            // 隐藏加载面板
            if (this.loadingPanel) {
                this.loadingPanel.style.opacity = '0';
                this.loadingPanel.style.transform = 'translate(-50%, -50%) scale(0.9)';
                
                setTimeout(() => {
                    this.loadingPanel.remove();
                }, 300);
            }
            
            // 显示欢迎通知
            this.showNotification('🎉 工作流优化系统已就绪', `
                已成功加载 ${this.loadingStatus.loaded} 个工作流模块：<br>
                • 开发工作流优化器 - 自动化测试和构建<br>
                • 用户工作流增强器 - 历史记录和批量处理<br>
                • 维护工作流管理器 - 系统监控和自动维护<br>
                <br>
                <strong>快捷键提示：</strong><br>
                • Ctrl+Shift+T: 快速验证<br>
                • Ctrl+Shift+H: 历史记录<br>
                • Ctrl+Shift+E: 快速导出
            `);
            
        }, 1000);
    }
    
    /**
     * 显示通知
     */
    showNotification(title, content) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            max-width: 500px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 10001;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
            transition: all 0.3s ease;
        `;
        
        notification.innerHTML = `
            <div style="
                padding: 16px 20px;
                border-bottom: 1px solid #e2e8f0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 12px 12px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            ">
                <h4 style="margin: 0; font-size: 16px;">${title}</h4>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    opacity: 0.8;
                ">×</button>
            </div>
            <div style="padding: 20px; font-size: 14px; line-height: 1.5;">
                ${content}
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 触发动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-20px)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 10000);
    }
    
    /**
     * 全局设置
     */
    showGlobalSettings() {
        console.log('📋 显示全局工作流设置');
        alert('全局设置面板开发中...');
    }
    
    /**
     * 导出统一报告
     */
    exportUnifiedReport() {
        console.log('📄 导出统一工作流报告');
        
        const report = {
            metadata: {
                title: '工作流优化系统统一报告',
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            },
            summary: {
                loadedModules: this.loadingStatus.loaded,
                failedModules: this.loadingStatus.failed,
                efficiencyStats: this.efficiencyStats
            },
            environment: this.environment,
            loadedModules: Array.from(this.loadedModules.keys()),
            moduleStates: Object.fromEntries(this.moduleStates)
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workflow-system-report-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        console.log('✅ 统一报告已导出');
    }
    
    /**
     * 显示帮助面板
     */
    showHelpPanel() {
        this.showNotification('📖 工作流系统帮助', `
            <strong>系统组成：</strong><br>
            • <strong>开发工作流优化器</strong>: 自动化测试、构建和验证<br>
            • <strong>用户工作流增强器</strong>: 历史记录、模板和批量处理<br>
            • <strong>维护工作流管理器</strong>: 系统监控、规则管理和自动维护<br>
            <br>
            <strong>使用建议：</strong><br>
            1. 开发时使用快速验证 (Ctrl+Shift+T)<br>
            2. 日常使用时启用历史记录和模板功能<br>
            3. 定期查看系统维护状态<br>
            4. 利用批量处理提高工作效率<br>
            <br>
            <strong>技术支持：</strong> 查看浏览器控制台获取详细日志
        `);
    }
}

// 自动初始化工作流集成加载器
if (typeof window !== 'undefined') {
    // 等待页面和其他脚本加载完成
    const initWorkflowIntegration = () => {
        window.workflowIntegrationLoader = new WorkflowIntegrationLoader({
            enableDevWorkflow: true,
            enableUserWorkflow: true,
            enableMaintenanceWorkflow: true,
            enableEfficiencyMonitoring: true,
            showUnifiedPanel: true,
            panelPosition: 'right',
            lazyLoading: true,
            loadingDelay: 2000
        });
    };
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initWorkflowIntegration, 2000);
        });
    } else {
        setTimeout(initWorkflowIntegration, 2000);
    }
}